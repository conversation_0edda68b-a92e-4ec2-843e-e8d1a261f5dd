# FHIR to OMOP CDM Transformation Pipeline

A comprehensive toolkit developed by AIO (Artificial Intelligence Orchestrator) for transforming healthcare data from HL7 FHIR format to OMOP Common Data Model (CDM), enabling standardized analytics and research.

## Overview

This internal repository provides a modular, extensible framework for transforming FHIR (Fast Healthcare Interoperability Resources) data to the OMOP (Observational Medical Outcomes Partnership) Common Data Model. It includes Python-based ETL tools, mapping strategies, and comprehensive documentation based on extensive research of existing implementations.

## Features

- **FHIR Server**: Local HAPI FHIR server with both PostgreSQL (production) and H2 (development) database support
- **Python-based ETL Pipeline**: Transform FHIR resources to OMOP CDM tables
- **Modular Architecture**: Separate mappers for different FHIR resource types
- **Comprehensive Documentation**: Detailed analysis of existing tools and approaches, with references to original sources
- **Mapping Strategies**: Well-documented mappings for key FHIR resources
- **Extensible Framework**: Easy to add support for additional FHIR resources
- **Validation Tools**: Ensure data quality throughout the transformation process
- **Performance Metrics**: Detailed system resource monitoring and performance analysis for FHIR data loading
- **Interactive Demo Application**: Streamlit-based application for visualizing FHIR data, loading resources, and exploring the server

## Architecture Overview

```mermaid
graph TD
    subgraph "Data Sources"
        A1[FHIR Server] -->|Export| A2[FHIR Resources]
        A3[FHIR Files] --> A2
    end

    subgraph "ETL Process"
        A2 -->|1 Extract| B[Resource Extraction]
        B -->|2 Transform| C[Mappers]
        D[Vocabulary Service] -->|Concept Mapping| C
        C -->|3 Load| E[OMOP CDM Database]
    end

    %% Apply styles with better contrast
    style A1 fill:#d770ad,stroke:#333,stroke-width:2px,color:#fff
    style A2 fill:#d770ad,stroke:#333,stroke-width:2px,color:#fff
    style A3 fill:#d770ad,stroke:#333,stroke-width:2px,color:#fff
    style B fill:#5d8aa8,stroke:#333,stroke-width:2px,color:#fff
    style C fill:#5d8aa8,stroke:#333,stroke-width:2px,color:#fff
    style D fill:#6b8e23,stroke:#333,stroke-width:2px,color:#fff
    style E fill:#b87333,stroke:#333,stroke-width:2px,color:#fff
```

This diagram shows the high-level flow of data through the system:

1. **Data Sources**: FHIR data comes from either a FHIR server or FHIR files
2. **Resource Extraction**: Raw FHIR resources are extracted and validated
3. **Mappers**: FHIR resources are transformed to OMOP format with help from the Vocabulary Service
4. **Vocabulary Service**: Provides standard concept mappings (e.g., SNOMED to OMOP concepts)
5. **OMOP Database**: Transformed data is loaded into OMOP CDM tables

For detailed architecture information, please see the [Reference Architecture](docs/architecture/reference_architecture.md) document.

## Repository Structure

```
fhir-omop/
├── data/                           # Data files
│   ├── sample_fhir/                # Sample FHIR resources
│   │   └── bulk-export/            # FHIR bulk export data in NDJSON format
│   └── vocabulary/                 # OMOP vocabulary files
├── demo/                           # Demo applications
│   └── demo_streamlit_app/         # Streamlit-based FHIR server demo
│       ├── app.py                  # Main Streamlit application
│       ├── environment.yml         # Conda environment configuration
│       ├── setup_and_run.sh        # Setup and run script
│       └── ui_components/          # Modular UI components
├── docs/                           # Documentation
│   ├── architecture/               # Architecture documentation
│   ├── guides/                     # User and developer guides
│   │   └── fhir/                   # FHIR server documentation
│   │       └── server/             # FHIR server setup and configuration
│   ├── mappings/                   # Mapping documentation
│   └── research/                   # Research and analysis
│       ├── code_examples/          # Code examples from other projects
│       └── repositories/           # Analysis of existing repositories
├── examples/                       # Usage examples
├── scripts/                        # Utility scripts
├── servers/                        # Server configurations
│   └── fhir-server/                # HAPI FHIR server
│       ├── docker-compose.yml      # H2 database configuration
│       ├── docker-compose-postgres.yml # PostgreSQL configuration
│       ├── scripts/                # Python scripts for server management
│       ├── start-fhir-server.sh    # Basic management script (H2)
│       └── manage-fhir-server.sh   # Advanced management script (H2/PostgreSQL)
├── src/                            # Source code
│   └── fhir_omop/                  # Main package
│       ├── etl/                    # ETL components
│       ├── mappers/                # FHIR to OMOP mappers
│       ├── utils/                  # Utility functions
│       └── validation/             # Data validation
└── tests/                          # Automated tests
```

## Getting Started

### Prerequisites

- [Docker](https://www.docker.com/products/docker-desktop/) (version 20.10.x or higher)
- [Docker Compose](https://docs.docker.com/compose/install/) (version 2.x or higher)
- [Miniconda](https://docs.conda.io/en/latest/miniconda.html) or [Anaconda](https://www.anaconda.com/products/distribution)
- Python 3.8 or higher (for scripts and ETL pipeline)
- OHDSI Athena account for vocabulary downloads
- UMLS account for CPT-4 reconstitution

### Setup

Follow our comprehensive [Setup Guide](docs/guides/setup.md) which covers:

1. Registering for required accounts (Athena, UMLS)
2. Downloading and configuring OMOP vocabularies
3. Setting up the FHIR server
   - [FHIR Server Documentation](docs/guides/fhir/server/00-index.md)
   - [PostgreSQL Migration Guide](docs/guides/fhir/server/postgresql-migration.md)
4. Setting up the OMOP database
5. Configuring the environment

### Installation

1. Clone the repository:
   ```bash
   git clone [internal-repository-url]/fhir-omop.git
   cd fhir-omop
   ```

2. Set up the Conda environment:
   ```bash
   # Create and activate the Conda environment
   conda env create -f environment.yml
   conda activate fhir-omop_env

   # Set up Jupyter kernel for this project
   python -m ipykernel install --user --name fhir-omop_env --display-name "Python (FHIR-OMOP)"
   ```

3. Configure the connection settings in `.env` file (created from `.env.example`)

   Note: This project uses Conda for environment management. All dependencies are defined in the `environment.yml` file.

### Basic Usage

#### Starting the FHIR Server

```bash
# Start with PostgreSQL (recommended for production)
cd servers/fhir-server
./manage-fhir-server.sh start postgres

# Or start with H2 (for development/testing)
cd servers/fhir-server
./start-fhir-server.sh start
```

#### Loading Sample Data

```bash
# Load sample data into the FHIR server using transaction bundles
cd servers/fhir-server

# Method 1: Direct Transaction Bundles (requires server configuration)
python scripts/transaction_bundles/ndjson_to_bundle.py --input-file ../data/sample_fhir/bulk-export/Patient.000.ndjson --output-file ../data/generated_bundles/Patient_bundle.json
python scripts/transaction_bundles/send_bundle.py --input-file ../data/generated_bundles/Patient_bundle.json --server-url http://localhost:8080/fhir

# Method 2: Selective Loading (works with default server configuration)
python scripts/transaction_bundles/selective_loader.py --data-dir ../data/sample_fhir/bulk-export --server-url http://localhost:8080/fhir --verify

# Method 3: Bulk Loading with Performance Metrics
# First convert NDJSON files to transaction bundles
python scripts/transaction_bundles/ndjson_to_bundle.py --input-dir ../data/sample_fhir/bulk-export --output-dir ../data/generated_bundles/bulk_export_bundles

# Then load all bundles with performance metrics
python scripts/transaction_bundles/load_all_bundles.py --bundle-dir ../data/generated_bundles/bulk_export_bundles --server-url http://localhost:8080/fhir --export-performance --test-id "initial_load_test"
```

For detailed instructions on data loading methods, see the [FHIR Data Loading Documentation](docs/guides/fhir/data-loading/quick-reference.md) and [Performance Metrics Documentation](servers/fhir-server/scripts/transaction_bundles/README_PERFORMANCE_METRICS.md).

#### Running the Interactive Demo Application

The Streamlit-based demo application provides an intuitive interface for interacting with the FHIR server:

```bash
# Set up and run the demo application
cd demo/demo_streamlit_app
chmod +x setup_and_run.sh
./setup_and_run.sh

# Or manually with conda
conda env create -f demo/demo_streamlit_app/environment.yml
conda activate fhir-demo
cd demo
streamlit run demo_streamlit_app/app.py
```

The application will be available at http://localhost:8501 and provides:
- Data loading interface with performance metrics
- Data exploration with FHIR queries
- Visualizations of resource distribution and patient demographics
- Server management capabilities

#### Running the ETL Pipeline

```bash
python scripts/run_pipeline.py
```

For more detailed usage, see the [User Guide](docs/guides/user_guide.md) in the documentation.

## Documentation

Comprehensive documentation is available in the `docs/` directory. All code includes references to original sources and implementation guides to ensure proper attribution and facilitate troubleshooting:

### Setup and Configuration
- [Setup Guide](docs/guides/setup.md) - Complete setup process with external registrations
- [Troubleshooting](docs/guides/troubleshooting.md) - Solutions for common issues

### Tutorials
- [Learning Path](docs/tutorials/learning_path.md) - Comprehensive learning path for FHIR to OMOP transformation
- [Introduction to FHIR](docs/tutorials/01_Introduction_to_FHIR.ipynb) - Learn the basics of FHIR standard
- [Introduction to OMOP CDM](docs/tutorials/02_Introduction_to_OMOP_CDM.ipynb) - Learn the basics of OMOP Common Data Model
- [Mapping FHIR to OMOP](docs/tutorials/03_Mapping_FHIR_to_OMOP.ipynb) - Learn how to map FHIR resources to OMOP tables
- [Data Visualization and Analysis](docs/tutorials/04_Data_Visualization_and_Analysis.ipynb) - Visualize and analyze transformed data
- [Athena Registration](docs/tutorials/athena_registration.md) - How to register on OHDSI Athena
- [Vocabulary Download](docs/tutorials/vocabulary_download.md) - How to download OMOP vocabularies
- [UMLS Registration](docs/tutorials/umls_registration.md) - How to register for UMLS API access

### Technical Documentation
- [Reference Architecture](docs/architecture/reference_architecture.md) - Detailed system architecture and design
- [FHIR Server Documentation](docs/guides/fhir/server/00-index.md) - Comprehensive guide for the HAPI FHIR server
  - [Server Setup](docs/guides/fhir/server/03-server_setup.md) - Setting up the FHIR server with H2 or PostgreSQL
  - [PostgreSQL Migration](docs/guides/fhir/server/postgresql-migration.md) - Migrating from H2 to PostgreSQL
  - [Data Interaction](docs/guides/fhir/server/09-data_interaction.md) - Methods for interacting with the FHIR server
- [FHIR Query Guide](docs/guides/fhir/api/fhir_query_guide.md) - Comprehensive guide on building effective FHIR queries
- [Database Setup](docs/guides/database_setup.md) - OMOP CDM database configuration overview
  - [PostgreSQL Setup](docs/guides/database_postgresql_setup.md) - Detailed PostgreSQL setup for production
  - [SQLite Setup](docs/guides/database_sqlite_setup.md) - Lightweight SQLite setup for development
- [Mapping Documentation](docs/mappings/) - Details on code system mappings
- [Research Analysis](docs/research/) - Analysis of existing FHIR to OMOP approaches

## Development

This is an internal project of AIO (Artificial Intelligence Orchestrator). For development guidelines, please refer to the [Contributing Guidelines](CONTRIBUTING.md) and [Coding Standards](docs/guides/development/standards.md).

### Developer Onboarding

- [Visualization Developer Onboarding](docs/guides/VISUALIZATION_DEVELOPER_ONBOARDING.md) - Guide for developers working on the visualization module

### Running Tests

For detailed testing instructions, run the tests with the following command:

```bash
conda activate fhir-omop_env
pytest
```

## Roadmap

- [x] Initial project setup and documentation
- [x] FHIR server implementation with H2 database
- [x] FHIR server implementation with PostgreSQL database
- [x] Sample data loading functionality
- [ ] OMOP CDM database setup
- [ ] Basic ETL pipeline implementation
- [ ] Support for key FHIR resources (Patient, Observation, Condition, etc.)
- [ ] Validation and quality assurance tools
- [ ] Performance optimization
- [ ] Comprehensive test suite
- [ ] CI/CD pipeline integration

## Proprietary Notice

This project is proprietary to AIO (Artificial Intelligence Orchestrator). All rights reserved. Unauthorized copying, distribution, or use is strictly prohibited.

## Acknowledgments

- OHDSI community for the OMOP CDM
- HL7 for the FHIR standard
- The research conducted on open-source projects that provided valuable insights
