# OMOP CDM Documentation

This section contains documentation related to the OMOP Common Data Model (CDM) components of the project. The OMOP module is designed to integrate with the existing FHIR module to create a complete FHIR-to-OMOP transformation pipeline.

## Getting Started

- [Introduction to OMOP CDM](introduction.md) - Overview of OMOP CDM and its role in the project
- [Architecture Overview](architecture.md) - OMOP module architecture and integration with FHIR
- [Implementation Roadmap](roadmap.md) - Phased approach to implementing the OMOP module

## Database Setup

- [Database Overview](database/overview.md) - Overview of database options for OMOP CDM
- [PostgreSQL Setup](database/postgresql_setup.md) - Setting up PostgreSQL for OMOP CDM
- [SQLite Setup](database/sqlite_setup.md) - Setting up SQLite for OMOP CDM (development)

## Vocabulary Management

- [Vocabulary Overview](vocabulary/overview.md) - Introduction to OMOP vocabularies
- [Athena Setup](vocabulary/athena_setup.md) - Configuring and using OHDSI Athena for vocabularies
- [Vocabulary Loading](vocabulary/loading.md) - Loading vocabularies into the OMOP database

## Data Model

- [CDM Overview](model/overview.md) - Overview of the OMOP CDM v5.4 data model
- [Table Descriptions](model/tables.md) - Detailed descriptions of OMOP CDM tables
- [Relationships](model/relationships.md) - Relationships between OMOP CDM tables

## FHIR-to-OMOP Mapping

- [Mapping Overview](mapping/overview.md) - Introduction to FHIR-to-OMOP mapping
- [Mapping Approach](mapping/approach.md) - Approach to mapping FHIR resources to OMOP tables
- [Mapping Reference](mapping/reference.md) - Reference mappings based on HL7 Vulcan FHIR-to-OMOP IG

## Implementation

- [Module Structure](implementation/structure.md) - Structure of the OMOP module
- [Database Layer](implementation/database.md) - Database access and management
- [Data Access Layer](implementation/dao.md) - Data Access Objects for OMOP tables
- [Integration](implementation/integration.md) - Integration with the FHIR module

## Directory Structure

```
omop/
├── introduction.md             # Introduction to OMOP CDM
├── architecture.md             # OMOP module architecture
├── roadmap.md                  # Implementation roadmap
├── database/                   # Database setup and configuration
│   ├── overview.md             # Overview of database options
│   ├── postgresql_setup.md     # PostgreSQL setup guide
│   └── sqlite_setup.md         # SQLite setup guide
├── vocabulary/                 # Vocabulary management
│   ├── overview.md             # Introduction to OMOP vocabularies
│   ├── athena_setup.md         # Athena setup guide
│   └── loading.md              # Vocabulary loading guide
├── model/                      # OMOP data model documentation
│   ├── overview.md             # Overview of the OMOP CDM
│   ├── tables.md               # Table descriptions
│   └── relationships.md        # Table relationships
├── mapping/                    # FHIR-to-OMOP mapping
│   ├── overview.md             # Introduction to mapping
│   ├── approach.md             # Mapping approach
│   └── reference.md            # Reference mappings
└── implementation/             # Implementation details
    ├── structure.md            # Module structure
    ├── database.md             # Database layer
    ├── dao.md                  # Data Access Objects
    └── integration.md          # Integration with FHIR module
```

## Official Resources

This documentation and implementation are based on the following official resources:

1. [HL7 Vulcan FHIR-to-OMOP Implementation Guide](https://build.fhir.org/ig/HL7/fhir-omop-ig/)
2. [OHDSI Common Data Model](https://github.com/OHDSI/CommonDataModel)
3. [OMOP CDM Documentation](https://ohdsi.github.io/CommonDataModel/)
4. [The Book of OHDSI](https://ohdsi.github.io/TheBookOfOhdsi/)
