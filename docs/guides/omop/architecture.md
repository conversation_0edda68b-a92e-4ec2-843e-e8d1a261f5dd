# OMOP Module Architecture

This document outlines the architecture of the OMOP module and its integration with the existing FHIR module to create a complete FHIR-to-OMOP transformation pipeline.

## Overall Architecture

The OMOP module is designed as a standalone component that can be integrated with the FHIR module. The architecture follows a modular design with clear separation of concerns, enabling flexibility, scalability, and maintainability.

```mermaid
graph TD
    subgraph "FHIR Module"
        A1[FHIR Server] -->|Bulk Export| B1[FHIR Resources]
        B1 -->|Extract| C1[Resource Extraction]
    end

    subgraph "OMOP Module"
        C1 -->|Transform| D1[Mappers]
        D1 -->|Load| E1[OMOP CDM Database]
        
        subgraph "Mappers"
            D1 --> D2[Patient Mapper]
            D1 --> D3[Encounter Mapper]
            D1 --> D4[Condition Mapper]
            D1 --> D5[Observation Mapper]
            D1 --> D6[Other Mappers...]
        end
        
        subgraph "OMOP CDM"
            E1 --> E2[Person]
            E1 --> E3[Visit Occurrence]
            E1 --> E4[Condition Occurrence]
            E1 --> E5[Measurement]
            E1 --> E6[Other Tables...]
        end
    end

    subgraph "Support Components"
        F1[Vocabulary Service] -->|Concept Mapping| D1
        F2[Configuration] --> C1
        F2 --> D1
        F2 --> E1
        F3[Utilities] --> C1
        F3 --> D1
        F3 --> E1
    end
```

## Module Components

The OMOP module consists of the following core components:

### 1. Database Layer

- **Database Manager**: Manages database connections and transactions
- **Schema Manager**: Handles schema creation and management
- **Vocabulary Manager**: Manages vocabulary loading and updates

### 2. Data Model Layer

- **SQLAlchemy Models**: Object-relational mapping for OMOP CDM tables
- **Model Relationships**: Defines relationships between OMOP tables
- **Model Validation**: Validates data against OMOP CDM constraints

### 3. Data Access Layer

- **Data Access Objects (DAOs)**: Provides CRUD operations for OMOP tables
- **Query Utilities**: Facilitates common queries and operations
- **Transaction Management**: Ensures data integrity across operations

### 4. Vocabulary Service

- **Concept Lookup**: Retrieves concepts by code, name, or ID
- **Concept Mapping**: Maps source concepts to standard concepts
- **Vocabulary Navigation**: Navigates concept hierarchies and relationships

### 5. Mapping Layer

- **Base Mapper**: Provides common mapping functionality
- **Resource-Specific Mappers**: Maps specific FHIR resources to OMOP tables
- **Mapping Validation**: Validates mappings against OMOP CDM constraints

### 6. Integration Layer

- **ETL Pipeline**: Orchestrates the extraction, transformation, and loading process
- **Configuration**: Manages configuration for both FHIR and OMOP components
- **Logging and Monitoring**: Tracks transformation process and performance

## Module Structure

The OMOP module is organized as follows:

```
src/fhir_omop/omop/
├── __init__.py
├── db/
│   ├── __init__.py
│   ├── db_manager.py         # Database connection and management
│   ├── schema_manager.py     # Schema creation and management
│   └── vocabulary_manager.py # Vocabulary loading and management
├── models/
│   ├── __init__.py
│   ├── base.py               # Base model class
│   ├── clinical.py           # Clinical domain models (Person, Visit, etc.)
│   ├── vocabulary.py         # Vocabulary domain models
│   └── metadata.py           # Metadata models
├── dao/
│   ├── __init__.py
│   ├── base_dao.py           # Base DAO with common operations
│   ├── person_dao.py         # Person table DAO
│   ├── visit_dao.py          # Visit_Occurrence table DAO
│   └── other_daos.py         # Other table DAOs
├── vocabulary/
│   ├── __init__.py
│   ├── concept_service.py    # Concept lookup and mapping service
│   └── cache.py              # Caching utilities for performance
└── mappers/
    ├── __init__.py
    ├── base_mapper.py        # Base mapper with common functionality
    ├── patient_mapper.py     # Patient to Person mapper
    ├── encounter_mapper.py   # Encounter to Visit_Occurrence mapper
    └── other_mappers.py      # Other resource mappers
```

## Integration with FHIR Module

The OMOP module integrates with the existing FHIR module through:

1. **Shared Configuration**: Common configuration for both modules
2. **ETL Pipeline**: Orchestrated extraction, transformation, and loading process
3. **Resource Mappers**: Mappers that transform FHIR resources to OMOP records
4. **Shared Utilities**: Common utilities for logging, error handling, etc.

## Data Flow

The data flow through the FHIR-to-OMOP transformation pipeline is as follows:

```mermaid
sequenceDiagram
    participant FHIR as FHIR Source
    participant Extract as Extraction Layer
    participant Map as Mappers
    participant Vocab as Vocabulary Service
    participant Load as Loading Layer
    participant OMOP as OMOP CDM Database

    FHIR->>Extract: FHIR Resources (JSON)
    Extract->>Map: Parsed Resources
    
    Map->>Vocab: Request Concept Mappings
    Vocab-->>Map: Standard Concepts
    
    Map->>Load: OMOP-Compatible Records
    Load->>OMOP: Insert into CDM Tables
    OMOP-->>Load: Confirmation
    
    Load-->>Map: Success/Failure
    Map-->>Extract: Processing Status
    Extract-->>FHIR: Completion Report
```

## Technology Stack

The OMOP module uses the following technologies:

- **Python**: Primary programming language
- **SQLAlchemy**: Object-relational mapping for database access
- **PostgreSQL/SQLite**: Database options for OMOP CDM
- **Pandas**: Data manipulation and transformation
- **FHIR Resources**: Python library for FHIR resource handling

## Design Principles

The OMOP module follows these design principles:

1. **Modularity**: Components are designed to be independent and reusable
2. **Separation of Concerns**: Clear separation between database, data access, and mapping layers
3. **Extensibility**: Easy to extend with new mappers and functionality
4. **Testability**: Components are designed to be easily testable
5. **Configuration-Driven**: Behavior can be configured without code changes
6. **Standards-Based**: Follows OHDSI and HL7 standards and best practices

## References

1. [HL7 Vulcan FHIR-to-OMOP Implementation Guide](https://build.fhir.org/ig/HL7/fhir-omop-ig/)
2. [OHDSI Common Data Model](https://github.com/OHDSI/CommonDataModel)
3. [OMOP CDM Documentation](https://ohdsi.github.io/CommonDataModel/)
4. [The Book of OHDSI](https://ohdsi.github.io/TheBookOfOhdsi/)
