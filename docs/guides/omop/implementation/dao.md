# OMOP Data Access Layer

This document describes the Data Access Object (DAO) layer of the OMOP module, which provides an interface for accessing and manipulating OMOP CDM tables.

## Overview

The Data Access Object (DAO) pattern provides an abstraction layer between the application and the database. It encapsulates the logic for database operations, making the code more maintainable and testable.

The DAO layer consists of the following components:

1. **Base DAO**: Provides common CRUD operations for all tables
2. **Table-Specific DAOs**: Provide specialized operations for specific tables
3. **Query Utilities**: Facilitate common queries and operations

These components are located in the `src/fhir_omop/omop/dao/` directory.

## Base DAO

The Base DAO (`base_dao.py`) provides common CRUD operations for all tables:

```python
"""
Base Data Access Object for OMOP CDM tables.
"""
import logging
from sqlalchemy.exc import SQLAlchemyError
from ..db.db_manager import OmopDBManager

# Set up logging
logger = logging.getLogger(__name__)

class BaseDAO:
    """
    Base Data Access Object with common CRUD operations.
    """
    def __init__(self, model_class, db_manager=None, connection_string=None):
        """
        Initialize the DAO.
        
        Args:
            model_class: SQLAlchemy model class
            db_manager (OmopDBManager, optional): Database manager instance.
            connection_string (str, optional): Database connection string.
        """
        self.model_class = model_class
        self.db_manager = db_manager or OmopDBManager(connection_string)
    
    def create(self, entity):
        """
        Create a new entity.
        
        Args:
            entity: Entity to create
            
        Returns:
            Entity: Created entity with ID
        """
        try:
            with self.db_manager.session() as session:
                session.add(entity)
                session.flush()  # Flush to get the ID
                return entity
        except SQLAlchemyError as e:
            logger.error(f"Error creating {self.model_class.__name__}: {e}")
            raise
    
    def create_batch(self, entities):
        """
        Create multiple entities in batch.
        
        Args:
            entities (list): List of entities to create
            
        Returns:
            list: Created entities with IDs
        """
        try:
            with self.db_manager.session() as session:
                session.add_all(entities)
                session.flush()  # Flush to get the IDs
                return entities
        except SQLAlchemyError as e:
            logger.error(f"Error creating batch of {self.model_class.__name__}: {e}")
            raise
    
    def get_by_id(self, entity_id):
        """
        Get entity by ID.
        
        Args:
            entity_id: Entity ID
            
        Returns:
            Entity or None: Found entity or None if not found
        """
        try:
            with self.db_manager.session() as session:
                return session.query(self.model_class).get(entity_id)
        except SQLAlchemyError as e:
            logger.error(f"Error getting {self.model_class.__name__} by ID {entity_id}: {e}")
            raise
    
    def get_all(self, limit=None, offset=None):
        """
        Get all entities.
        
        Args:
            limit (int, optional): Maximum number of entities to return
            offset (int, optional): Number of entities to skip
            
        Returns:
            list: List of entities
        """
        try:
            with self.db_manager.session() as session:
                query = session.query(self.model_class)
                
                if offset is not None:
                    query = query.offset(offset)
                
                if limit is not None:
                    query = query.limit(limit)
                
                return query.all()
        except SQLAlchemyError as e:
            logger.error(f"Error getting all {self.model_class.__name__}: {e}")
            raise
    
    def update(self, entity):
        """
        Update an entity.
        
        Args:
            entity: Entity to update
            
        Returns:
            Entity: Updated entity
        """
        try:
            with self.db_manager.session() as session:
                session.merge(entity)
                return entity
        except SQLAlchemyError as e:
            logger.error(f"Error updating {self.model_class.__name__}: {e}")
            raise
    
    def delete(self, entity_id):
        """
        Delete an entity by ID.
        
        Args:
            entity_id: Entity ID
            
        Returns:
            bool: True if entity was deleted, False otherwise
        """
        try:
            with self.db_manager.session() as session:
                entity = session.query(self.model_class).get(entity_id)
                if entity:
                    session.delete(entity)
                    return True
                return False
        except SQLAlchemyError as e:
            logger.error(f"Error deleting {self.model_class.__name__} with ID {entity_id}: {e}")
            raise
    
    def count(self):
        """
        Count all entities.
        
        Returns:
            int: Number of entities
        """
        try:
            with self.db_manager.session() as session:
                return session.query(self.model_class).count()
        except SQLAlchemyError as e:
            logger.error(f"Error counting {self.model_class.__name__}: {e}")
            raise
```

## Table-Specific DAOs

Table-specific DAOs provide specialized operations for specific tables. Here are examples for key tables:

### Person DAO

The Person DAO (`person_dao.py`) provides operations for the Person table:

```python
"""
Data Access Object for OMOP CDM Person table.
"""
import logging
from sqlalchemy import text
from sqlalchemy.exc import SQLAlchemyError
from .base_dao import BaseDAO
from ..models.clinical import Person

# Set up logging
logger = logging.getLogger(__name__)

class PersonDAO(BaseDAO):
    """
    Data Access Object for OMOP CDM Person table.
    """
    def __init__(self, db_manager=None, connection_string=None):
        """
        Initialize the DAO.
        
        Args:
            db_manager (OmopDBManager, optional): Database manager instance.
            connection_string (str, optional): Database connection string.
        """
        super().__init__(Person, db_manager, connection_string)
    
    def get_by_source_value(self, source_value):
        """
        Get person by source value.
        
        Args:
            source_value (str): Person source value
            
        Returns:
            Person or None: Found person or None if not found
        """
        try:
            with self.db_manager.session() as session:
                return session.query(Person).filter(
                    Person.person_source_value == source_value
                ).first()
        except SQLAlchemyError as e:
            logger.error(f"Error getting Person by source value {source_value}: {e}")
            raise
    
    def get_by_gender_concept(self, gender_concept_id):
        """
        Get persons by gender concept ID.
        
        Args:
            gender_concept_id (int): Gender concept ID
            
        Returns:
            list: List of persons with the specified gender concept ID
        """
        try:
            with self.db_manager.session() as session:
                return session.query(Person).filter(
                    Person.gender_concept_id == gender_concept_id
                ).all()
        except SQLAlchemyError as e:
            logger.error(f"Error getting Persons by gender concept ID {gender_concept_id}: {e}")
            raise
    
    def get_by_year_of_birth_range(self, start_year, end_year):
        """
        Get persons by year of birth range.
        
        Args:
            start_year (int): Start year (inclusive)
            end_year (int): End year (inclusive)
            
        Returns:
            list: List of persons born within the specified year range
        """
        try:
            with self.db_manager.session() as session:
                return session.query(Person).filter(
                    Person.year_of_birth >= start_year,
                    Person.year_of_birth <= end_year
                ).all()
        except SQLAlchemyError as e:
            logger.error(f"Error getting Persons by year of birth range {start_year}-{end_year}: {e}")
            raise
    
    def get_demographics_summary(self):
        """
        Get demographics summary.
        
        Returns:
            dict: Dictionary with demographic statistics
        """
        try:
            with self.db_manager.session() as session:
                # Get gender distribution
                gender_query = """
                SELECT c.concept_name, COUNT(*) as count
                FROM person p
                JOIN concept c ON p.gender_concept_id = c.concept_id
                GROUP BY c.concept_name
                """
                gender_results = session.execute(text(gender_query)).fetchall()
                
                # Get age distribution
                age_query = """
                SELECT
                    CASE
                        WHEN year_of_birth > EXTRACT(YEAR FROM CURRENT_DATE) - 18 THEN '<18'
                        WHEN year_of_birth > EXTRACT(YEAR FROM CURRENT_DATE) - 30 THEN '18-29'
                        WHEN year_of_birth > EXTRACT(YEAR FROM CURRENT_DATE) - 45 THEN '30-44'
                        WHEN year_of_birth > EXTRACT(YEAR FROM CURRENT_DATE) - 65 THEN '45-64'
                        ELSE '65+'
                    END as age_group,
                    COUNT(*) as count
                FROM person
                GROUP BY age_group
                ORDER BY age_group
                """
                age_results = session.execute(text(age_query)).fetchall()
                
                # Get race distribution
                race_query = """
                SELECT c.concept_name, COUNT(*) as count
                FROM person p
                JOIN concept c ON p.race_concept_id = c.concept_id
                GROUP BY c.concept_name
                """
                race_results = session.execute(text(race_query)).fetchall()
                
                # Get ethnicity distribution
                ethnicity_query = """
                SELECT c.concept_name, COUNT(*) as count
                FROM person p
                JOIN concept c ON p.ethnicity_concept_id = c.concept_id
                GROUP BY c.concept_name
                """
                ethnicity_results = session.execute(text(ethnicity_query)).fetchall()
                
                # Compile results
                return {
                    'gender': {row[0]: row[1] for row in gender_results},
                    'age_group': {row[0]: row[1] for row in age_results},
                    'race': {row[0]: row[1] for row in race_results},
                    'ethnicity': {row[0]: row[1] for row in ethnicity_results},
                    'total': self.count()
                }
        except SQLAlchemyError as e:
            logger.error(f"Error getting demographics summary: {e}")
            raise
```

### Visit Occurrence DAO

The Visit Occurrence DAO (`visit_dao.py`) provides operations for the Visit_Occurrence table:

```python
"""
Data Access Object for OMOP CDM Visit_Occurrence table.
"""
import logging
from sqlalchemy import text
from sqlalchemy.exc import SQLAlchemyError
from .base_dao import BaseDAO
from ..models.clinical import VisitOccurrence

# Set up logging
logger = logging.getLogger(__name__)

class VisitOccurrenceDAO(BaseDAO):
    """
    Data Access Object for OMOP CDM Visit_Occurrence table.
    """
    def __init__(self, db_manager=None, connection_string=None):
        """
        Initialize the DAO.
        
        Args:
            db_manager (OmopDBManager, optional): Database manager instance.
            connection_string (str, optional): Database connection string.
        """
        super().__init__(VisitOccurrence, db_manager, connection_string)
    
    def get_by_person_id(self, person_id):
        """
        Get visits by person ID.
        
        Args:
            person_id (int): Person ID
            
        Returns:
            list: List of visits for the specified person
        """
        try:
            with self.db_manager.session() as session:
                return session.query(VisitOccurrence).filter(
                    VisitOccurrence.person_id == person_id
                ).all()
        except SQLAlchemyError as e:
            logger.error(f"Error getting Visit_Occurrences by person ID {person_id}: {e}")
            raise
    
    def get_by_source_value(self, source_value):
        """
        Get visit by source value.
        
        Args:
            source_value (str): Visit source value
            
        Returns:
            VisitOccurrence or None: Found visit or None if not found
        """
        try:
            with self.db_manager.session() as session:
                return session.query(VisitOccurrence).filter(
                    VisitOccurrence.visit_source_value == source_value
                ).first()
        except SQLAlchemyError as e:
            logger.error(f"Error getting Visit_Occurrence by source value {source_value}: {e}")
            raise
    
    def get_by_date_range(self, start_date, end_date):
        """
        Get visits by date range.
        
        Args:
            start_date (date): Start date (inclusive)
            end_date (date): End date (inclusive)
            
        Returns:
            list: List of visits within the specified date range
        """
        try:
            with self.db_manager.session() as session:
                return session.query(VisitOccurrence).filter(
                    VisitOccurrence.visit_start_date >= start_date,
                    VisitOccurrence.visit_end_date <= end_date
                ).all()
        except SQLAlchemyError as e:
            logger.error(f"Error getting Visit_Occurrences by date range {start_date}-{end_date}: {e}")
            raise
    
    def get_visit_summary(self):
        """
        Get visit summary.
        
        Returns:
            dict: Dictionary with visit statistics
        """
        try:
            with self.db_manager.session() as session:
                # Get visit type distribution
                type_query = """
                SELECT c.concept_name, COUNT(*) as count
                FROM visit_occurrence v
                JOIN concept c ON v.visit_concept_id = c.concept_id
                GROUP BY c.concept_name
                """
                type_results = session.execute(text(type_query)).fetchall()
                
                # Get visit length distribution
                length_query = """
                SELECT
                    CASE
                        WHEN visit_end_date - visit_start_date = 0 THEN 'Same day'
                        WHEN visit_end_date - visit_start_date < 3 THEN '1-2 days'
                        WHEN visit_end_date - visit_start_date < 7 THEN '3-6 days'
                        WHEN visit_end_date - visit_start_date < 14 THEN '1-2 weeks'
                        ELSE '2+ weeks'
                    END as length_group,
                    COUNT(*) as count
                FROM visit_occurrence
                GROUP BY length_group
                ORDER BY length_group
                """
                length_results = session.execute(text(length_query)).fetchall()
                
                # Compile results
                return {
                    'visit_type': {row[0]: row[1] for row in type_results},
                    'length_group': {row[0]: row[1] for row in length_results},
                    'total': self.count()
                }
        except SQLAlchemyError as e:
            logger.error(f"Error getting visit summary: {e}")
            raise
```

## Usage Examples

### Basic CRUD Operations

```python
from fhir_omop.omop.dao.person_dao import PersonDAO
from fhir_omop.omop.models.clinical import Person

# Initialize DAO
person_dao = PersonDAO()

# Create a new person
person = Person(
    gender_concept_id=8507,  # Male
    year_of_birth=1974,
    month_of_birth=12,
    day_of_birth=25,
    race_concept_id=0,
    ethnicity_concept_id=0,
    person_source_value="example"
)
person = person_dao.create(person)
print(f"Created person with ID: {person.person_id}")

# Get person by ID
person = person_dao.get_by_id(person.person_id)
print(f"Retrieved person: {person.person_source_value}")

# Update person
person.year_of_birth = 1975
person = person_dao.update(person)
print(f"Updated person: {person.year_of_birth}")

# Delete person
deleted = person_dao.delete(person.person_id)
print(f"Deleted person: {deleted}")
```

### Specialized Operations

```python
from fhir_omop.omop.dao.person_dao import PersonDAO
from fhir_omop.omop.dao.visit_dao import VisitOccurrenceDAO

# Initialize DAOs
person_dao = PersonDAO()
visit_dao = VisitOccurrenceDAO()

# Get person by source value
person = person_dao.get_by_source_value("example")
if person:
    print(f"Found person: {person.person_id}")
    
    # Get visits for person
    visits = visit_dao.get_by_person_id(person.person_id)
    print(f"Found {len(visits)} visits for person")
    
    # Get demographics summary
    demographics = person_dao.get_demographics_summary()
    print(f"Total persons: {demographics['total']}")
    print(f"Gender distribution: {demographics['gender']}")
    print(f"Age group distribution: {demographics['age_group']}")
```

## References

1. [SQLAlchemy Documentation](https://docs.sqlalchemy.org/)
2. [OMOP CDM Documentation](https://ohdsi.github.io/CommonDataModel/)
3. [Data Access Object Pattern](https://www.tutorialspoint.com/design_pattern/data_access_object_pattern.htm)
