# OMOP Database Layer

This document describes the database layer of the OMOP module, which handles database connections, schema management, and vocabulary loading.

## Overview

The database layer consists of the following components:

1. **Database Manager**: Manages database connections and transactions
2. **Schema Manager**: Handles schema creation and management
3. **Vocabulary Manager**: Manages vocabulary loading and updates

These components are located in the `src/fhir_omop/omop/db/` directory.

## Database Manager

The Database Manager (`db_manager.py`) is responsible for:

- Establishing and managing database connections
- Creating and managing database sessions
- Handling transactions
- Providing utility functions for database operations

### Implementation

```python
"""
Database manager for OMOP CDM.
"""
import logging
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, scoped_session
from contextlib import contextmanager
from ...config import OMOP_DB_CONNECTION_STRING

# Set up logging
logger = logging.getLogger(__name__)

class OmopDBManager:
    """
    Manager for OMOP CDM database operations.
    """
    def __init__(self, connection_string=None):
        """
        Initialize the database manager.
        
        Args:
            connection_string (str, optional): Database connection string.
                If not provided, uses the one from config.
        """
        self.connection_string = connection_string or OMOP_DB_CONNECTION_STRING
        self.engine = None
        self.session_factory = None
        self._setup_engine()
    
    def _setup_engine(self):
        """Set up the SQLAlchemy engine and session factory."""
        try:
            self.engine = create_engine(self.connection_string)
            self.session_factory = scoped_session(sessionmaker(bind=self.engine))
            logger.info(f"Connected to OMOP database: {self.connection_string}")
        except Exception as e:
            logger.error(f"Failed to connect to OMOP database: {e}")
            raise
    
    @contextmanager
    def session(self):
        """
        Provide a transactional scope around a series of operations.
        
        Yields:
            sqlalchemy.orm.Session: Database session
        """
        session = self.session_factory()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error(f"Database transaction error: {e}")
            raise
        finally:
            session.close()
    
    def create_tables(self):
        """Create all tables defined in SQLAlchemy models."""
        from ..models.base import Base
        try:
            Base.metadata.create_all(self.engine)
            logger.info("Created OMOP CDM tables")
        except Exception as e:
            logger.error(f"Failed to create OMOP CDM tables: {e}")
            raise
    
    def drop_tables(self):
        """Drop all tables defined in SQLAlchemy models."""
        from ..models.base import Base
        try:
            Base.metadata.drop_all(self.engine)
            logger.info("Dropped OMOP CDM tables")
        except Exception as e:
            logger.error(f"Failed to drop OMOP CDM tables: {e}")
            raise
    
    def check_connection(self):
        """
        Check if the database connection is working.
        
        Returns:
            bool: True if connection is working, False otherwise
        """
        try:
            with self.engine.connect() as conn:
                conn.execute("SELECT 1")
            return True
        except Exception as e:
            logger.error(f"Database connection check failed: {e}")
            return False
    
    def get_database_type(self):
        """
        Get the type of database (sqlite or postgresql).
        
        Returns:
            str: 'sqlite' or 'postgresql'
        """
        if 'sqlite' in self.connection_string.lower():
            return 'sqlite'
        elif 'postgresql' in self.connection_string.lower():
            return 'postgresql'
        else:
            return 'unknown'
```

### Usage

```python
from fhir_omop.omop.db.db_manager import OmopDBManager

# Initialize database manager
db_manager = OmopDBManager('postgresql://username:password@localhost:5432/omop_cdm')

# Check connection
if db_manager.check_connection():
    print("Connected to database")
else:
    print("Failed to connect to database")

# Use session for database operations
with db_manager.session() as session:
    # Perform database operations
    result = session.execute("SELECT COUNT(*) FROM concept").scalar()
    print(f"Number of concepts: {result}")
```

## Schema Manager

The Schema Manager (`schema_manager.py`) is responsible for:

- Creating the OMOP CDM schema
- Managing schema versions
- Validating the schema
- Handling schema migrations

### Implementation

```python
"""
Schema manager for OMOP CDM.
"""
import logging
import os
from pathlib import Path
import sqlite3
import subprocess
from sqlalchemy import text
from .db_manager import OmopDBManager

# Set up logging
logger = logging.getLogger(__name__)

class OmopSchemaManager:
    """
    Manager for OMOP CDM schema operations.
    """
    def __init__(self, db_manager=None, connection_string=None):
        """
        Initialize the schema manager.
        
        Args:
            db_manager (OmopDBManager, optional): Database manager instance.
            connection_string (str, optional): Database connection string.
        """
        self.db_manager = db_manager or OmopDBManager(connection_string)
        self.db_type = self.db_manager.get_database_type()
    
    def create_schema(self, ddl_path=None):
        """
        Create OMOP CDM schema using DDL files.
        
        Args:
            ddl_path (str, optional): Path to DDL files directory.
                If not provided, uses default DDL files.
        
        Returns:
            bool: True if schema creation was successful, False otherwise
        """
        if self.db_type == 'sqlite':
            return self._create_sqlite_schema()
        elif self.db_type == 'postgresql':
            return self._create_postgres_schema(ddl_path)
        else:
            logger.error(f"Unsupported database type: {self.db_type}")
            return False
    
    def _create_sqlite_schema(self):
        """
        Create OMOP CDM schema in SQLite database.
        
        Returns:
            bool: True if schema creation was successful, False otherwise
        """
        try:
            # Use SQLAlchemy models to create tables
            self.db_manager.create_tables()
            logger.info("Created OMOP CDM schema in SQLite database")
            return True
        except Exception as e:
            logger.error(f"Failed to create OMOP CDM schema in SQLite: {e}")
            return False
    
    def _create_postgres_schema(self, ddl_path=None):
        """
        Create OMOP CDM schema in PostgreSQL database using DDL files.
        
        Args:
            ddl_path (str, optional): Path to DDL files directory.
        
        Returns:
            bool: True if schema creation was successful, False otherwise
        """
        # Implementation details omitted for brevity
        pass
```

### Usage

```python
from fhir_omop.omop.db.db_manager import OmopDBManager
from fhir_omop.omop.db.schema_manager import OmopSchemaManager

# Initialize database manager
db_manager = OmopDBManager('postgresql://username:password@localhost:5432/omop_cdm')

# Initialize schema manager
schema_manager = OmopSchemaManager(db_manager)

# Create schema
if schema_manager.create_schema():
    print("Schema created successfully")
else:
    print("Failed to create schema")
```

## Vocabulary Manager

The Vocabulary Manager (`vocabulary_manager.py`) is responsible for:

- Loading vocabulary files into the database
- Validating vocabulary data
- Managing vocabulary updates
- Providing utilities for vocabulary operations

### Implementation

```python
"""
Vocabulary manager for OMOP CDM.
"""
import logging
import os
import pandas as pd
from pathlib import Path
from sqlalchemy import text
from .db_manager import OmopDBManager

# Set up logging
logger = logging.getLogger(__name__)

class VocabularyManager:
    """
    Manager for OMOP CDM vocabulary operations.
    """
    def __init__(self, db_manager=None, connection_string=None):
        """
        Initialize the vocabulary manager.
        
        Args:
            db_manager (OmopDBManager, optional): Database manager instance.
            connection_string (str, optional): Database connection string.
        """
        self.db_manager = db_manager or OmopDBManager(connection_string)
        self.db_type = self.db_manager.get_database_type()
    
    def load_vocabulary(self, vocabulary_dir):
        """
        Load OMOP CDM vocabulary files into the database.
        
        Args:
            vocabulary_dir (str): Path to directory containing vocabulary CSV files.
        
        Returns:
            bool: True if vocabulary loading was successful, False otherwise
        """
        # Implementation details omitted for brevity
        pass
```

### Usage

```python
from fhir_omop.omop.db.db_manager import OmopDBManager
from fhir_omop.omop.db.vocabulary_manager import VocabularyManager

# Initialize database manager
db_manager = OmopDBManager('postgresql://username:password@localhost:5432/omop_cdm')

# Initialize vocabulary manager
vocab_manager = VocabularyManager(db_manager)

# Load vocabularies
if vocab_manager.load_vocabulary('/path/to/vocabulary'):
    print("Vocabularies loaded successfully")
else:
    print("Failed to load vocabularies")

# Check vocabulary loading
vocab_counts = vocab_manager.check_vocabulary()
for table, count in vocab_counts.items():
    print(f"{table}: {count} records")
```

## Configuration

The database layer is configured through environment variables and configuration files:

### Environment Variables

```
# Database Configuration
OMOP_DB_HOST=localhost
OMOP_DB_PORT=5432
OMOP_DB_NAME=omop_cdm
OMOP_DB_USER=username
OMOP_DB_PASSWORD=password
OMOP_DB_SCHEMA=public

# Vocabulary Configuration
OMOP_VOCABULARY_PATH=/path/to/vocabulary
```

### Configuration Module

```python
"""
Configuration for OMOP module.
"""
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Database configuration
OMOP_DB_HOST = os.getenv('OMOP_DB_HOST', 'localhost')
OMOP_DB_PORT = os.getenv('OMOP_DB_PORT', '5432')
OMOP_DB_NAME = os.getenv('OMOP_DB_NAME', 'omop_cdm')
OMOP_DB_USER = os.getenv('OMOP_DB_USER', 'postgres')
OMOP_DB_PASSWORD = os.getenv('OMOP_DB_PASSWORD', 'postgres')
OMOP_DB_SCHEMA = os.getenv('OMOP_DB_SCHEMA', 'public')

# Construct connection string
OMOP_DB_CONNECTION_STRING = f"postgresql://{OMOP_DB_USER}:{OMOP_DB_PASSWORD}@{OMOP_DB_HOST}:{OMOP_DB_PORT}/{OMOP_DB_NAME}"

# SQLite connection string (for development)
OMOP_SQLITE_PATH = os.getenv('OMOP_SQLITE_PATH', 'data/omop_cdm.db')
OMOP_SQLITE_CONNECTION_STRING = f"sqlite:///{OMOP_SQLITE_PATH}"

# Vocabulary configuration
OMOP_VOCABULARY_PATH = os.getenv('OMOP_VOCABULARY_PATH', 'data/vocabulary')
```

## References

1. [SQLAlchemy Documentation](https://docs.sqlalchemy.org/)
2. [OMOP CDM Documentation](https://ohdsi.github.io/CommonDataModel/)
3. [PostgreSQL Documentation](https://www.postgresql.org/docs/)
4. [SQLite Documentation](https://www.sqlite.org/docs.html)
