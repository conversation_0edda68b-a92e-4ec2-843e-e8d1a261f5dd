# Integration with FHIR Module

This document describes how the OMOP module integrates with the existing FHIR module to create a complete FHIR-to-OMOP transformation pipeline.

## Integration Overview

The integration between the FHIR and OMOP modules follows these principles:

1. **Loose Coupling**: Modules are loosely coupled to allow independent development
2. **Clear Interfaces**: Well-defined interfaces between modules
3. **Shared Configuration**: Common configuration for both modules
4. **Consistent Error Handling**: Consistent approach to error handling
5. **End-to-End Testing**: Comprehensive testing of the integrated pipeline

## Integration Architecture

The integration architecture is as follows:

```mermaid
graph TD
    subgraph "FHIR Module"
        A1[FHIR Server] -->|Bulk Export| B1[FHIR Resources]
        B1 -->|Extract| C1[Resource Extraction]
    end

    subgraph "OMOP Module"
        C1 -->|Transform| D1[Mappers]
        D1 -->|Load| E1[OMOP CDM Database]
    end

    subgraph "Shared Components"
        F1[Configuration]
        F2[Logging]
        F3[Error Handling]
        F4[Utilities]
    end

    F1 --> A1
    F1 --> C1
    F1 --> D1
    F1 --> E1
    
    F2 --> A1
    F2 --> C1
    F2 --> D1
    F2 --> E1
    
    F3 --> A1
    F3 --> C1
    F3 --> D1
    F3 --> E1
    
    F4 --> A1
    F4 --> C1
    F4 --> D1
    F4 --> E1
```

## Integration Components

### 1. ETL Pipeline

The ETL (Extract, Transform, Load) pipeline orchestrates the end-to-end process:

```python
"""
ETL pipeline for FHIR-to-OMOP transformation.
"""
import logging
from fhir_omop.fhir.client import FhirClient
from fhir_omop.fhir.extractor import ResourceExtractor
from fhir_omop.omop.mappers.patient_mapper import PatientMapper
from fhir_omop.omop.mappers.encounter_mapper import EncounterMapper
from fhir_omop.omop.db.db_manager import OmopDBManager
from fhir_omop.omop.vocabulary.concept_service import ConceptService

# Set up logging
logger = logging.getLogger(__name__)

class FhirToOmopPipeline:
    """
    Pipeline for transforming FHIR resources to OMOP CDM.
    """
    def __init__(self, fhir_url, omop_connection_string):
        """
        Initialize the pipeline.
        
        Args:
            fhir_url (str): URL of the FHIR server
            omop_connection_string (str): Connection string for the OMOP database
        """
        # Initialize FHIR components
        self.fhir_client = FhirClient(fhir_url)
        self.resource_extractor = ResourceExtractor(self.fhir_client)
        
        # Initialize OMOP components
        self.db_manager = OmopDBManager(omop_connection_string)
        self.concept_service = ConceptService(self.db_manager)
        
        # Initialize mappers
        self.patient_mapper = PatientMapper(self.concept_service, self.db_manager)
        self.encounter_mapper = EncounterMapper(self.concept_service, self.db_manager)
    
    def run(self, resource_types=None, batch_size=100):
        """
        Run the pipeline.
        
        Args:
            resource_types (list, optional): List of resource types to process.
                If not provided, processes all supported resource types.
            batch_size (int, optional): Number of resources to process in each batch.
                
        Returns:
            dict: Pipeline statistics
        """
        # Default resource types if not provided
        if resource_types is None:
            resource_types = ['Patient', 'Encounter', 'Condition', 'Observation']
        
        # Initialize statistics
        stats = {
            'extracted': 0,
            'transformed': 0,
            'loaded': 0,
            'errors': 0,
            'by_resource_type': {}
        }
        
        # Process each resource type
        for resource_type in resource_types:
            logger.info(f"Processing {resource_type} resources")
            
            # Initialize statistics for this resource type
            stats['by_resource_type'][resource_type] = {
                'extracted': 0,
                'transformed': 0,
                'loaded': 0,
                'errors': 0
            }
            
            # Extract resources
            resources = self.resource_extractor.extract(resource_type, batch_size)
            
            # Process resources
            for resource_batch in resources:
                try:
                    # Update statistics
                    batch_size = len(resource_batch)
                    stats['extracted'] += batch_size
                    stats['by_resource_type'][resource_type]['extracted'] += batch_size
                    
                    # Transform and load resources
                    if resource_type == 'Patient':
                        self._process_patients(resource_batch, stats)
                    elif resource_type == 'Encounter':
                        self._process_encounters(resource_batch, stats)
                    # Add other resource types as needed
                    
                except Exception as e:
                    logger.error(f"Error processing {resource_type} batch: {e}")
                    stats['errors'] += 1
                    stats['by_resource_type'][resource_type]['errors'] += 1
        
        return stats
    
    def _process_patients(self, resources, stats):
        """Process Patient resources."""
        transformed = []
        
        # Transform resources
        for resource in resources:
            try:
                person = self.patient_mapper.map(resource)
                transformed.append(person)
                stats['transformed'] += 1
                stats['by_resource_type']['Patient']['transformed'] += 1
            except Exception as e:
                logger.error(f"Error transforming Patient {resource.get('id')}: {e}")
                stats['errors'] += 1
                stats['by_resource_type']['Patient']['errors'] += 1
        
        # Load transformed resources
        if transformed:
            try:
                self.patient_mapper.save(transformed)
                stats['loaded'] += len(transformed)
                stats['by_resource_type']['Patient']['loaded'] += len(transformed)
            except Exception as e:
                logger.error(f"Error loading Person records: {e}")
                stats['errors'] += 1
                stats['by_resource_type']['Patient']['errors'] += 1
    
    def _process_encounters(self, resources, stats):
        """Process Encounter resources."""
        # Similar implementation as _process_patients
        pass
```

### 2. Shared Configuration

The shared configuration module provides configuration for both FHIR and OMOP components:

```python
"""
Shared configuration for FHIR and OMOP modules.
"""
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# FHIR configuration
FHIR_SERVER_URL = os.getenv('FHIR_SERVER_URL', 'http://localhost:8080/fhir')
FHIR_SERVER_USERNAME = os.getenv('FHIR_SERVER_USERNAME', '')
FHIR_SERVER_PASSWORD = os.getenv('FHIR_SERVER_PASSWORD', '')
FHIR_VERSION = os.getenv('FHIR_VERSION', 'R4')

# OMOP configuration
OMOP_DB_HOST = os.getenv('OMOP_DB_HOST', 'localhost')
OMOP_DB_PORT = os.getenv('OMOP_DB_PORT', '5432')
OMOP_DB_NAME = os.getenv('OMOP_DB_NAME', 'omop_cdm')
OMOP_DB_USER = os.getenv('OMOP_DB_USER', 'postgres')
OMOP_DB_PASSWORD = os.getenv('OMOP_DB_PASSWORD', 'postgres')
OMOP_DB_SCHEMA = os.getenv('OMOP_DB_SCHEMA', 'public')

# Construct connection strings
OMOP_DB_CONNECTION_STRING = f"postgresql://{OMOP_DB_USER}:{OMOP_DB_PASSWORD}@{OMOP_DB_HOST}:{OMOP_DB_PORT}/{OMOP_DB_NAME}"
OMOP_SQLITE_PATH = os.getenv('OMOP_SQLITE_PATH', 'data/omop_cdm.db')
OMOP_SQLITE_CONNECTION_STRING = f"sqlite:///{OMOP_SQLITE_PATH}"

# Vocabulary configuration
OMOP_VOCABULARY_PATH = os.getenv('OMOP_VOCABULARY_PATH', 'data/vocabulary')

# ETL configuration
ETL_BATCH_SIZE = int(os.getenv('ETL_BATCH_SIZE', '100'))
ETL_MAX_WORKERS = int(os.getenv('ETL_MAX_WORKERS', '4'))
ETL_TIMEOUT = int(os.getenv('ETL_TIMEOUT', '3600'))
```

### 3. Shared Utilities

Shared utilities provide common functionality for both modules:

```python
"""
Shared utilities for FHIR and OMOP modules.
"""
import logging
import time
import json
from datetime import datetime

# Set up logging
logger = logging.getLogger(__name__)

def parse_date(date_str):
    """
    Parse a date string into a datetime object.
    
    Args:
        date_str (str): Date string
        
    Returns:
        datetime: Parsed datetime object
    """
    if not date_str:
        return None
    
    formats = [
        '%Y-%m-%d',
        '%Y-%m-%dT%H:%M:%S',
        '%Y-%m-%dT%H:%M:%S.%f',
        '%Y-%m-%dT%H:%M:%S%z',
        '%Y-%m-%dT%H:%M:%S.%f%z'
    ]
    
    for fmt in formats:
        try:
            return datetime.strptime(date_str, fmt)
        except ValueError:
            continue
    
    logger.warning(f"Could not parse date: {date_str}")
    return None

def measure_time(func):
    """
    Decorator to measure execution time of a function.
    
    Args:
        func: Function to measure
        
    Returns:
        function: Wrapped function
    """
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        logger.debug(f"{func.__name__} executed in {end_time - start_time:.2f} seconds")
        return result
    return wrapper

def safe_get(obj, path, default=None):
    """
    Safely get a value from a nested object.
    
    Args:
        obj (dict): Object to get value from
        path (str): Path to value (e.g., 'a.b.c')
        default: Default value if path not found
        
    Returns:
        Value at path or default
    """
    if not obj:
        return default
    
    parts = path.split('.')
    current = obj
    
    for part in parts:
        if isinstance(current, dict) and part in current:
            current = current[part]
        elif isinstance(current, list) and part.isdigit() and int(part) < len(current):
            current = current[int(part)]
        else:
            return default
    
    return current
```

### 4. Command-Line Interface

A command-line interface provides a way to run the pipeline:

```python
"""
Command-line interface for FHIR-to-OMOP pipeline.
"""
import argparse
import logging
import sys
from fhir_omop.pipeline import FhirToOmopPipeline
from fhir_omop.config import FHIR_SERVER_URL, OMOP_DB_CONNECTION_STRING

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    """Main entry point for the CLI."""
    parser = argparse.ArgumentParser(description='FHIR-to-OMOP Transformation Pipeline')
    
    parser.add_argument(
        '--fhir-url',
        default=FHIR_SERVER_URL,
        help='URL of the FHIR server'
    )
    
    parser.add_argument(
        '--omop-connection',
        default=OMOP_DB_CONNECTION_STRING,
        help='Connection string for the OMOP database'
    )
    
    parser.add_argument(
        '--resource-types',
        nargs='+',
        default=['Patient', 'Encounter', 'Condition', 'Observation'],
        help='Resource types to process'
    )
    
    parser.add_argument(
        '--batch-size',
        type=int,
        default=100,
        help='Number of resources to process in each batch'
    )
    
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Enable verbose logging'
    )
    
    args = parser.parse_args()
    
    # Set log level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Run pipeline
    try:
        pipeline = FhirToOmopPipeline(args.fhir_url, args.omop_connection)
        stats = pipeline.run(args.resource_types, args.batch_size)
        
        # Print statistics
        print("\nPipeline Statistics:")
        print(f"Extracted: {stats['extracted']}")
        print(f"Transformed: {stats['transformed']}")
        print(f"Loaded: {stats['loaded']}")
        print(f"Errors: {stats['errors']}")
        
        print("\nBy Resource Type:")
        for resource_type, type_stats in stats['by_resource_type'].items():
            print(f"  {resource_type}:")
            print(f"    Extracted: {type_stats['extracted']}")
            print(f"    Transformed: {type_stats['transformed']}")
            print(f"    Loaded: {type_stats['loaded']}")
            print(f"    Errors: {type_stats['errors']}")
        
        return 0
    except Exception as e:
        logger.error(f"Pipeline error: {e}")
        return 1

if __name__ == '__main__':
    sys.exit(main())
```

## Integration Testing

Integration testing ensures that the FHIR and OMOP modules work together correctly:

```python
"""
Integration tests for FHIR-to-OMOP pipeline.
"""
import unittest
from fhir_omop.pipeline import FhirToOmopPipeline
from fhir_omop.fhir.client import FhirClient
from fhir_omop.omop.db.db_manager import OmopDBManager
from fhir_omop.omop.db.schema_manager import OmopSchemaManager

class TestFhirToOmopPipeline(unittest.TestCase):
    """Tests for the FHIR-to-OMOP pipeline."""
    
    @classmethod
    def setUpClass(cls):
        """Set up test environment."""
        # Use test FHIR server and database
        cls.fhir_url = 'http://localhost:8080/fhir'
        cls.omop_connection_string = 'sqlite:///data/test_omop_cdm.db'
        
        # Set up OMOP database
        cls.db_manager = OmopDBManager(cls.omop_connection_string)
        cls.schema_manager = OmopSchemaManager(cls.db_manager)
        cls.schema_manager.create_schema()
        
        # Set up FHIR client
        cls.fhir_client = FhirClient(cls.fhir_url)
        
        # Set up pipeline
        cls.pipeline = FhirToOmopPipeline(cls.fhir_url, cls.omop_connection_string)
    
    @classmethod
    def tearDownClass(cls):
        """Clean up test environment."""
        # Drop OMOP database
        cls.schema_manager.drop_schema()
    
    def test_pipeline_end_to_end(self):
        """Test end-to-end pipeline."""
        # Run pipeline with test data
        stats = self.pipeline.run(['Patient', 'Encounter'], batch_size=10)
        
        # Check statistics
        self.assertGreater(stats['extracted'], 0)
        self.assertGreater(stats['transformed'], 0)
        self.assertGreater(stats['loaded'], 0)
        
        # Check database
        with self.db_manager.session() as session:
            person_count = session.execute("SELECT COUNT(*) FROM person").scalar()
            visit_count = session.execute("SELECT COUNT(*) FROM visit_occurrence").scalar()
            
            self.assertGreater(person_count, 0)
            self.assertGreater(visit_count, 0)
```

## References

1. [HL7 Vulcan FHIR-to-OMOP Implementation Guide](https://build.fhir.org/ig/HL7/fhir-omop-ig/)
2. [OHDSI ETL Best Practices](https://ohdsi.github.io/CommonDataModel/etlBestPractices.html)
3. [FHIR R4 Documentation](https://hl7.org/fhir/R4/)
4. [OMOP CDM Documentation](https://ohdsi.github.io/CommonDataModel/)
