# OMOP Module Structure

This document outlines the structure of the OMOP module, including its components, organization, and relationships.

## Module Organization

The OMOP module is organized as follows:

```
src/fhir_omop/omop/
├── __init__.py
├── db/
│   ├── __init__.py
│   ├── db_manager.py         # Database connection and management
│   ├── schema_manager.py     # Schema creation and management
│   └── vocabulary_manager.py # Vocabulary loading and management
├── models/
│   ├── __init__.py
│   ├── base.py               # Base model class
│   ├── clinical.py           # Clinical domain models (Person, Visit, etc.)
│   ├── vocabulary.py         # Vocabulary domain models
│   └── metadata.py           # Metadata models
├── dao/
│   ├── __init__.py
│   ├── base_dao.py           # Base DAO with common operations
│   ├── person_dao.py         # Person table DAO
│   ├── visit_dao.py          # Visit_Occurrence table DAO
│   └── other_daos.py         # Other table DAOs
├── vocabulary/
│   ├── __init__.py
│   ├── concept_service.py    # Concept lookup and mapping service
│   └── cache.py              # Caching utilities for performance
└── mappers/
    ├── __init__.py
    ├── base_mapper.py        # Base mapper with common functionality
    ├── patient_mapper.py     # Patient to Person mapper
    ├── encounter_mapper.py   # Encounter to Visit_Occurrence mapper
    └── other_mappers.py      # Other resource mappers
```

## Component Descriptions

### Database Layer (`db/`)

The database layer handles database connections, schema management, and vocabulary loading:

- **db_manager.py**: Manages database connections and transactions
- **schema_manager.py**: Handles schema creation and management
- **vocabulary_manager.py**: Manages vocabulary loading and updates

### Data Model Layer (`models/`)

The data model layer defines SQLAlchemy models for OMOP CDM tables:

- **base.py**: Defines the base model class and common functionality
- **clinical.py**: Defines models for clinical tables (Person, Visit_Occurrence, etc.)
- **vocabulary.py**: Defines models for vocabulary tables (Concept, Vocabulary, etc.)
- **metadata.py**: Defines models for metadata tables (Metadata, CDM_Source, etc.)

### Data Access Layer (`dao/`)

The data access layer provides CRUD operations for OMOP tables:

- **base_dao.py**: Defines the base DAO class with common operations
- **person_dao.py**: Provides operations for the Person table
- **visit_dao.py**: Provides operations for the Visit_Occurrence table
- **other_daos.py**: Provides operations for other tables

### Vocabulary Service (`vocabulary/`)

The vocabulary service handles concept lookup and mapping:

- **concept_service.py**: Provides concept lookup and mapping functionality
- **cache.py**: Implements caching for frequently used concepts

### Mapping Layer (`mappers/`)

The mapping layer transforms FHIR resources to OMOP records:

- **base_mapper.py**: Defines the base mapper class with common functionality
- **patient_mapper.py**: Maps Patient resources to Person records
- **encounter_mapper.py**: Maps Encounter resources to Visit_Occurrence records
- **other_mappers.py**: Maps other resources to their respective tables

## Component Relationships

The components of the OMOP module interact as follows:

```mermaid
graph TD
    A[Mappers] --> B[DAOs]
    A --> C[Vocabulary Service]
    B --> D[Models]
    B --> E[Database Manager]
    C --> E
    E --> F[Database]
```

1. **Mappers** use the **Vocabulary Service** to map FHIR codes to OMOP concepts
2. **Mappers** use **DAOs** to save mapped records to the database
3. **DAOs** use **Models** to represent OMOP records
4. **DAOs** use the **Database Manager** to interact with the database
5. **Vocabulary Service** uses the **Database Manager** to query concepts

## Module Initialization

The OMOP module is initialized as follows:

```python
from fhir_omop.omop.db.db_manager import OmopDBManager
from fhir_omop.omop.db.schema_manager import OmopSchemaManager
from fhir_omop.omop.vocabulary.concept_service import ConceptService
from fhir_omop.omop.dao.person_dao import PersonDAO
from fhir_omop.omop.mappers.patient_mapper import PatientMapper

# Initialize database manager
db_manager = OmopDBManager('postgresql://username:password@localhost:5432/omop_cdm')

# Initialize schema manager and create schema if needed
schema_manager = OmopSchemaManager(db_manager)
schema_manager.create_schema()

# Initialize vocabulary service
concept_service = ConceptService(db_manager)

# Initialize DAOs
person_dao = PersonDAO(db_manager)

# Initialize mappers
patient_mapper = PatientMapper(concept_service, person_dao)
```

## Module Configuration

The OMOP module is configured through environment variables and configuration files:

### Environment Variables

```
# Database Configuration
OMOP_DB_HOST=localhost
OMOP_DB_PORT=5432
OMOP_DB_NAME=omop_cdm
OMOP_DB_USER=username
OMOP_DB_PASSWORD=password
OMOP_DB_SCHEMA=public

# Vocabulary Configuration
OMOP_VOCABULARY_PATH=/path/to/vocabulary

# Mapping Configuration
OMOP_MAPPING_CONFIG=/path/to/mapping_config.json
```

### Configuration Files

```json
// mapping_config.json
{
  "default_concepts": {
    "gender_unknown": 0,
    "race_unknown": 0,
    "ethnicity_unknown": 0,
    "visit_type_ehr": 44818518
  },
  "code_system_map": {
    "http://snomed.info/sct": "SNOMED",
    "http://loinc.org": "LOINC",
    "http://www.nlm.nih.gov/research/umls/rxnorm": "RxNorm",
    "http://hl7.org/fhir/sid/icd-10-cm": "ICD10CM"
  }
}
```

## Module Usage

The OMOP module is used as follows:

```python
# Map a FHIR Patient resource to an OMOP Person record
patient_resource = {
    "resourceType": "Patient",
    "id": "example",
    "gender": "male",
    "birthDate": "1974-12-25"
}

# Map the resource
person_record = patient_mapper.map(patient_resource)

# Save the record
patient_mapper.save(person_record)
```

## Module Extension

The OMOP module can be extended in the following ways:

1. **Add New Models**: Add new SQLAlchemy models for additional tables
2. **Add New DAOs**: Add new DAOs for additional tables
3. **Add New Mappers**: Add new mappers for additional FHIR resources
4. **Enhance Vocabulary Service**: Add new functionality to the vocabulary service
5. **Add New Database Support**: Add support for additional database types

## References

1. [SQLAlchemy Documentation](https://docs.sqlalchemy.org/)
2. [OMOP CDM Documentation](https://ohdsi.github.io/CommonDataModel/)
3. [FHIR Resources Documentation](https://hl7.org/fhir/resourcelist.html)
4. [HL7 Vulcan FHIR-to-OMOP Implementation Guide](https://build.fhir.org/ig/HL7/fhir-omop-ig/)
