# FHIR-to-OMOP Mapping Overview

This document provides an overview of the mapping process from FHIR resources to OMOP CDM tables. The mapping is based on the HL7 Vulcan FHIR-to-OMOP Implementation Guide and other established best practices.

## Introduction to FHIR-to-OMOP Mapping

Mapping FHIR resources to OMOP CDM involves:

1. **Structural Mapping**: Mapping FHIR resources to OMOP tables
2. **Semantic Mapping**: Translating FHIR terminologies to OMOP standard concepts
3. **Data Transformation**: Converting data formats and units
4. **Relationship Preservation**: Maintaining relationships between clinical entities

## Mapping Challenges

FHIR and OMOP have different design philosophies and structures, which creates several challenges:

1. **Terminology Differences**: FHIR uses multiple code systems, while OMOP uses standardized concepts
2. **Structural Differences**: FHIR is resource-based, while OMOP is domain-based
3. **Granularity Differences**: FHIR may have more detailed information than OMOP can represent
4. **Temporal Representation**: Different approaches to representing time and events
5. **Identifier Management**: Different approaches to identifying entities

## Core Resource Mappings

The following table shows the core mappings between FHIR resources and OMOP tables:

| FHIR Resource | OMOP Table | Notes |
|---------------|------------|-------|
| Patient | PERSON | Demographics and identifiers |
| Encounter | VISIT_OCCURRENCE | Healthcare encounters |
| Condition | CONDITION_OCCURRENCE | Diagnoses and problems |
| MedicationRequest | DRUG_EXPOSURE | Medication orders and prescriptions |
| MedicationAdministration | DRUG_EXPOSURE | Medication administrations |
| MedicationDispense | DRUG_EXPOSURE | Medication dispensing |
| Procedure | PROCEDURE_OCCURRENCE | Procedures performed |
| Observation (lab) | MEASUREMENT | Laboratory results and vital signs |
| Observation (other) | OBSERVATION | Other observations |
| Device | DEVICE_EXPOSURE | Medical devices used |
| Immunization | DRUG_EXPOSURE | Vaccinations |
| AllergyIntolerance | CONDITION_OCCURRENCE | Allergies and intolerances |

## Mapping Approach

The mapping process follows these general steps:

1. **Extract FHIR Resources**: Retrieve FHIR resources from the source
2. **Validate Resources**: Ensure resources are valid and contain required data
3. **Map Person Information**: Create PERSON records from Patient resources
4. **Map Visits**: Create VISIT_OCCURRENCE records from Encounter resources
5. **Map Clinical Events**: Map clinical events (conditions, drugs, procedures, etc.)
6. **Establish Relationships**: Link clinical events to persons and visits
7. **Validate Mappings**: Ensure mappings meet OMOP CDM constraints

## Terminology Mapping

Terminology mapping is a critical part of the FHIR-to-OMOP transformation:

1. **Code System Mapping**: Map FHIR code systems to OMOP vocabularies
   - SNOMED CT → SNOMED
   - LOINC → LOINC
   - RxNorm → RxNorm
   - ICD-10 → ICD10CM
   - etc.

2. **Concept Mapping**: Map FHIR codes to OMOP concepts
   - Look up source concepts
   - Map to standard concepts
   - Handle unmapped concepts

3. **Domain Assignment**: Ensure concepts are used in appropriate domains
   - Condition concepts in CONDITION_OCCURRENCE
   - Drug concepts in DRUG_EXPOSURE
   - Procedure concepts in PROCEDURE_OCCURRENCE
   - etc.

## Mapping Examples

### Patient to Person

```
FHIR Patient:
{
  "resourceType": "Patient",
  "id": "example",
  "gender": "male",
  "birthDate": "1974-12-25",
  "name": [
    {
      "family": "Smith",
      "given": ["John"]
    }
  ]
}

OMOP Person:
{
  "person_id": <generated>,
  "gender_concept_id": 8507, // SNOMED concept for "Male"
  "year_of_birth": 1974,
  "month_of_birth": 12,
  "day_of_birth": 25,
  "birth_datetime": "1974-12-25T00:00:00",
  "race_concept_id": 0, // Unknown if not specified
  "ethnicity_concept_id": 0, // Unknown if not specified
  "person_source_value": "example"
}
```

### Encounter to Visit_Occurrence

```
FHIR Encounter:
{
  "resourceType": "Encounter",
  "id": "example",
  "status": "finished",
  "class": {
    "system": "http://terminology.hl7.org/CodeSystem/v3-ActCode",
    "code": "AMB"
  },
  "subject": {
    "reference": "Patient/example"
  },
  "period": {
    "start": "2023-01-15T09:00:00Z",
    "end": "2023-01-15T11:30:00Z"
  }
}

OMOP Visit_Occurrence:
{
  "visit_occurrence_id": <generated>,
  "person_id": <person_id>, // From Patient/example
  "visit_concept_id": 9202, // Ambulatory Visit
  "visit_start_date": "2023-01-15",
  "visit_start_datetime": "2023-01-15T09:00:00Z",
  "visit_end_date": "2023-01-15",
  "visit_end_datetime": "2023-01-15T11:30:00Z",
  "visit_type_concept_id": 44818518, // Visit derived from EHR
  "visit_source_value": "example"
}
```

## Mapping Implementation

The mapping is implemented through a set of mapper classes:

1. **Base Mapper**: Provides common mapping functionality
2. **Resource-Specific Mappers**: Maps specific FHIR resources to OMOP tables
   - Patient Mapper
   - Encounter Mapper
   - Condition Mapper
   - etc.
3. **Vocabulary Service**: Handles terminology mapping

## Mapping Validation

Mapping validation ensures that the transformed data meets OMOP CDM constraints:

1. **Structural Validation**: Ensures data conforms to OMOP CDM schema
2. **Referential Integrity**: Ensures relationships between tables are valid
3. **Concept Validation**: Ensures concepts are used in appropriate domains
4. **Data Quality Checks**: Ensures data meets quality standards

## References

1. [HL7 Vulcan FHIR-to-OMOP Implementation Guide](https://build.fhir.org/ig/HL7/fhir-omop-ig/)
2. [OHDSI ETL Best Practices](https://ohdsi.github.io/CommonDataModel/etlBestPractices.html)
3. [OMOP CDM Documentation](https://ohdsi.github.io/CommonDataModel/)
4. [The Book of OHDSI](https://ohdsi.github.io/TheBookOfOhdsi/)
