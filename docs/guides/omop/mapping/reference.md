# FHIR-to-OMOP Mapping Reference

This document provides reference mappings from FHIR resources to OMOP CDM tables, based on the HL7 Vulcan FHIR-to-OMOP Implementation Guide and other established best practices.

## Patient to Person

### Mapping Table

| FHIR Patient | OMOP Person | Notes |
|--------------|-------------|-------|
| id | person_source_value | Patient identifier |
| gender | gender_concept_id | Mapped to standard concept |
| birthDate | year_of_birth, month_of_birth, day_of_birth, birth_datetime | Split into components |
| deceasedBoolean, deceasedDateTime | - | Not directly mapped in OMOP CDM v5.4 |
| address | location_id | Mapped to Location table |
| name | - | Not directly mapped in OMOP CDM |
| telecom | - | Not directly mapped in OMOP CDM |
| identifier | - | Stored in source value or extension tables |
| extension | - | Stored in extension tables if needed |

### Code Mappings

#### Gender

| FHIR Code | OMOP Concept ID | Concept Name |
|-----------|----------------|--------------|
| male | 8507 | MALE |
| female | 8532 | FEMALE |
| other | 8521 | OTHER |
| unknown | 0 | No matching concept |

### Example

```
FHIR Patient:
{
  "resourceType": "Patient",
  "id": "example",
  "gender": "male",
  "birthDate": "1974-12-25",
  "name": [
    {
      "family": "Smith",
      "given": ["John"]
    }
  ]
}

OMOP Person:
{
  "person_id": <generated>,
  "gender_concept_id": 8507,
  "year_of_birth": 1974,
  "month_of_birth": 12,
  "day_of_birth": 25,
  "birth_datetime": "1974-12-25T00:00:00",
  "race_concept_id": 0,
  "ethnicity_concept_id": 0,
  "person_source_value": "example"
}
```

## Encounter to Visit_Occurrence

### Mapping Table

| FHIR Encounter | OMOP Visit_Occurrence | Notes |
|----------------|----------------------|-------|
| id | visit_source_value | Encounter identifier |
| subject | person_id | Reference to Person |
| class | visit_concept_id | Mapped to standard concept |
| type | visit_concept_id | Used if class not available |
| period.start | visit_start_date, visit_start_datetime | Start of visit |
| period.end | visit_end_date, visit_end_datetime | End of visit |
| serviceProvider | care_site_id | Reference to Care_Site |
| participant.individual | provider_id | Reference to Provider |
| hospitalization.admitSource | admitted_from_concept_id | Source of admission |
| hospitalization.dischargeDisposition | discharge_to_concept_id | Discharge disposition |
| episodeOfCare | preceding_visit_occurrence_id | Previous visit |
| status | - | Not directly mapped in OMOP CDM |
| priority | - | Not directly mapped in OMOP CDM |
| location | - | Not directly mapped in OMOP CDM |

### Code Mappings

#### Encounter Class

| FHIR Code | OMOP Concept ID | Concept Name |
|-----------|----------------|--------------|
| AMB | 9202 | Ambulatory Visit |
| IMP | 9201 | Inpatient Visit |
| EMER | 9203 | Emergency Room Visit |
| HH | 9202 | Home Visit |
| VR | 9202 | Virtual Visit |
| other | 0 | No matching concept |

### Example

```
FHIR Encounter:
{
  "resourceType": "Encounter",
  "id": "example",
  "status": "finished",
  "class": {
    "system": "http://terminology.hl7.org/CodeSystem/v3-ActCode",
    "code": "AMB"
  },
  "subject": {
    "reference": "Patient/example"
  },
  "period": {
    "start": "2023-01-15T09:00:00Z",
    "end": "2023-01-15T11:30:00Z"
  }
}

OMOP Visit_Occurrence:
{
  "visit_occurrence_id": <generated>,
  "person_id": <person_id>,
  "visit_concept_id": 9202,
  "visit_start_date": "2023-01-15",
  "visit_start_datetime": "2023-01-15T09:00:00Z",
  "visit_end_date": "2023-01-15",
  "visit_end_datetime": "2023-01-15T11:30:00Z",
  "visit_type_concept_id": 44818518,
  "visit_source_value": "example"
}
```

## Condition to Condition_Occurrence

### Mapping Table

| FHIR Condition | OMOP Condition_Occurrence | Notes |
|----------------|--------------------------|-------|
| id | condition_source_value | Condition identifier |
| subject | person_id | Reference to Person |
| encounter | visit_occurrence_id | Reference to Visit_Occurrence |
| code | condition_concept_id, condition_source_concept_id, condition_source_value | Mapped to standard concept |
| onsetDateTime | condition_start_date, condition_start_datetime | Start of condition |
| abatementDateTime | condition_end_date, condition_end_datetime | End of condition |
| recordedDate | - | Not directly mapped in OMOP CDM |
| asserter | provider_id | Reference to Provider |
| clinicalStatus | condition_status_concept_id | Status of condition |
| verificationStatus | - | Not directly mapped in OMOP CDM |
| category | condition_type_concept_id | Type of condition |
| severity | - | Not directly mapped in OMOP CDM |
| bodySite | - | Not directly mapped in OMOP CDM |
| stage | - | Not directly mapped in OMOP CDM |
| evidence | - | Not directly mapped in OMOP CDM |

### Code Mappings

#### Clinical Status

| FHIR Code | OMOP Concept ID | Concept Name |
|-----------|----------------|--------------|
| active | 4230359 | Active |
| recurrence | 4230359 | Active |
| relapse | 4230359 | Active |
| inactive | 4260793 | Inactive |
| remission | 4260793 | Inactive |
| resolved | 4260793 | Inactive |

### Example

```
FHIR Condition:
{
  "resourceType": "Condition",
  "id": "example",
  "clinicalStatus": {
    "coding": [
      {
        "system": "http://terminology.hl7.org/CodeSystem/condition-clinical",
        "code": "active"
      }
    ]
  },
  "code": {
    "coding": [
      {
        "system": "http://snomed.info/sct",
        "code": "44054006",
        "display": "Diabetes mellitus type 2"
      }
    ]
  },
  "subject": {
    "reference": "Patient/example"
  },
  "onsetDateTime": "2022-01-01"
}

OMOP Condition_Occurrence:
{
  "condition_occurrence_id": <generated>,
  "person_id": <person_id>,
  "condition_concept_id": 201826,
  "condition_start_date": "2022-01-01",
  "condition_start_datetime": "2022-01-01T00:00:00",
  "condition_end_date": null,
  "condition_end_datetime": null,
  "condition_type_concept_id": 32020,
  "condition_status_concept_id": 4230359,
  "condition_source_value": "44054006",
  "condition_source_concept_id": 45581031
}
```

## Observation to Measurement/Observation

FHIR Observations can map to either OMOP Measurement or OMOP Observation tables, depending on the type of observation.

### Mapping to Measurement

| FHIR Observation | OMOP Measurement | Notes |
|------------------|-----------------|-------|
| id | measurement_source_value | Observation identifier |
| subject | person_id | Reference to Person |
| encounter | visit_occurrence_id | Reference to Visit_Occurrence |
| code | measurement_concept_id, measurement_source_concept_id, measurement_source_value | Mapped to standard concept |
| effectiveDateTime | measurement_date, measurement_datetime | Date of measurement |
| valueQuantity.value | value_as_number | Numeric value |
| valueQuantity.unit | unit_concept_id, unit_source_value | Unit of measurement |
| valueCodeableConcept | value_as_concept_id | Coded value |
| dataAbsentReason | - | Not directly mapped in OMOP CDM |
| interpretation | operator_concept_id | Interpretation of result |
| referenceRange | range_low, range_high | Reference range |
| performer | provider_id | Reference to Provider |
| status | - | Not directly mapped in OMOP CDM |
| category | measurement_type_concept_id | Type of measurement |

### Mapping to Observation

| FHIR Observation | OMOP Observation | Notes |
|------------------|-----------------|-------|
| id | observation_source_value | Observation identifier |
| subject | person_id | Reference to Person |
| encounter | visit_occurrence_id | Reference to Visit_Occurrence |
| code | observation_concept_id, observation_source_concept_id, observation_source_value | Mapped to standard concept |
| effectiveDateTime | observation_date, observation_datetime | Date of observation |
| valueQuantity.value | value_as_number | Numeric value |
| valueQuantity.unit | unit_concept_id, unit_source_value | Unit of measurement |
| valueCodeableConcept | value_as_concept_id | Coded value |
| valueString | value_as_string | String value |
| dataAbsentReason | - | Not directly mapped in OMOP CDM |
| performer | provider_id | Reference to Provider |
| status | - | Not directly mapped in OMOP CDM |
| category | observation_type_concept_id | Type of observation |

### Example

```
FHIR Observation (Lab Test):
{
  "resourceType": "Observation",
  "id": "example",
  "status": "final",
  "category": [
    {
      "coding": [
        {
          "system": "http://terminology.hl7.org/CodeSystem/observation-category",
          "code": "laboratory"
        }
      ]
    }
  ],
  "code": {
    "coding": [
      {
        "system": "http://loinc.org",
        "code": "2339-0",
        "display": "Glucose [Mass/volume] in Blood"
      }
    ]
  },
  "subject": {
    "reference": "Patient/example"
  },
  "effectiveDateTime": "2023-01-15T10:30:00Z",
  "valueQuantity": {
    "value": 95,
    "unit": "mg/dL",
    "system": "http://unitsofmeasure.org",
    "code": "mg/dL"
  }
}

OMOP Measurement:
{
  "measurement_id": <generated>,
  "person_id": <person_id>,
  "measurement_concept_id": 3004501,
  "measurement_date": "2023-01-15",
  "measurement_datetime": "2023-01-15T10:30:00Z",
  "measurement_type_concept_id": 44818702,
  "operator_concept_id": 0,
  "value_as_number": 95,
  "value_as_concept_id": 0,
  "unit_concept_id": 8840,
  "range_low": null,
  "range_high": null,
  "provider_id": null,
  "visit_occurrence_id": null,
  "measurement_source_value": "2339-0",
  "measurement_source_concept_id": 3027114,
  "unit_source_value": "mg/dL"
}
```

## References

1. [HL7 Vulcan FHIR-to-OMOP Implementation Guide](https://build.fhir.org/ig/HL7/fhir-omop-ig/)
2. [OHDSI ETL Best Practices](https://ohdsi.github.io/CommonDataModel/etlBestPractices.html)
3. [FHIR R4 Documentation](https://hl7.org/fhir/R4/)
4. [OMOP CDM Documentation](https://ohdsi.github.io/CommonDataModel/)
