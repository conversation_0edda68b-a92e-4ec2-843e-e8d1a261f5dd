# OMOP CDM Overview

The Observational Medical Outcomes Partnership (OMOP) Common Data Model (CDM) is a standardized data model designed to facilitate the systematic analysis of disparate observational databases. This document provides an overview of the OMOP CDM v5.4 structure and its key components.

## OMOP CDM v5.4

OMOP CDM v5.4 is the current stable version of the OMOP CDM. It includes:

- 37 tables organized in 6 categories
- Standardized vocabulary tables
- Clinical data tables
- Health system data tables
- Derived tables
- Metadata tables
- Cost tables

## Data Model Categories

### 1. Standardized Vocabularies

These tables define the standardized vocabularies used in the OMOP CDM:

| Table | Description |
|-------|-------------|
| `CONCEPT` | Contains all concepts across all vocabularies |
| `VOCABULARY` | Contains information about the vocabularies |
| `DOMAIN` | Defines domains for concepts |
| `CONCEPT_CLASS` | Defines classes for concepts |
| `CONCEPT_RELATIONSHIP` | Defines relationships between concepts |
| `RELATIONSHIP` | Defines types of relationships |
| `CONCEPT_SYNONYM` | Contains alternative names for concepts |
| `CONCEPT_ANCESTOR` | Contains hierarchical relationships |
| `SOURCE_TO_CONCEPT_MAP` | Maps source codes to standard concepts |
| `DRUG_STRENGTH` | Contains drug strength information |

### 2. Clinical Data Tables

These tables store patient-level clinical data:

| Table | Description |
|-------|-------------|
| `PERSON` | Demographic information about patients |
| `OBSERVATION_PERIOD` | Time periods when a person is observed |
| `VISIT_OCCURRENCE` | Encounters with healthcare providers |
| `VISIT_DETAIL` | Detailed information about visits |
| `CONDITION_OCCURRENCE` | Conditions or diagnoses |
| `DRUG_EXPOSURE` | Medications and drugs |
| `PROCEDURE_OCCURRENCE` | Procedures performed |
| `DEVICE_EXPOSURE` | Medical devices used |
| `MEASUREMENT` | Measurements and lab tests |
| `OBSERVATION` | Observations that don't fit other domains |
| `SPECIMEN` | Specimens collected |
| `NOTE` | Clinical notes |
| `NOTE_NLP` | Natural language processing results from notes |
| `FACT_RELATIONSHIP` | Relationships between facts |

### 3. Health System Data Tables

These tables represent the healthcare delivery structure:

| Table | Description |
|-------|-------------|
| `LOCATION` | Geographic locations |
| `CARE_SITE` | Places where care is delivered |
| `PROVIDER` | Healthcare providers |

### 4. Derived Tables

These tables contain derived elements for analysis:

| Table | Description |
|-------|-------------|
| `DRUG_ERA` | Periods of continuous drug exposure |
| `DOSE_ERA` | Periods of constant drug dose |
| `CONDITION_ERA` | Periods of continuous condition |

### 5. Metadata Tables

These tables provide information about the data itself:

| Table | Description |
|-------|-------------|
| `METADATA` | Information about the CDM instance |
| `CDM_SOURCE` | Information about the source data |

### 6. Cost Tables

These tables capture financial information:

| Table | Description |
|-------|-------------|
| `COST` | Cost information for clinical events |
| `PAYER_PLAN_PERIOD` | Coverage periods for insurance plans |

## Key Concepts

### Person-Centric Model

The OMOP CDM is centered around the Person table, with all clinical events linked to a person:

```mermaid
graph TD
    Person --> VisitOccurrence
    Person --> ConditionOccurrence
    Person --> DrugExposure
    Person --> ProcedureOccurrence
    Person --> Measurement
    Person --> Observation
    Person --> DeviceExposure
    
    VisitOccurrence --> ConditionOccurrence
    VisitOccurrence --> DrugExposure
    VisitOccurrence --> ProcedureOccurrence
    VisitOccurrence --> Measurement
    VisitOccurrence --> Observation
    VisitOccurrence --> DeviceExposure
```

### Standardized Vocabularies

All clinical events are represented using standardized concepts from the vocabulary tables:

```mermaid
graph TD
    Concept --> ConditionOccurrence
    Concept --> DrugExposure
    Concept --> ProcedureOccurrence
    Concept --> Measurement
    Concept --> Observation
    Concept --> DeviceExposure
    
    ConceptRelationship --> Concept
    ConceptAncestor --> Concept
    Vocabulary --> Concept
    Domain --> Concept
    ConceptClass --> Concept
```

### Observation Period

The Observation Period table defines when a person is observed in the healthcare system:

```mermaid
graph TD
    Person --> ObservationPeriod
    ObservationPeriod --> VisitOccurrence
    ObservationPeriod --> ConditionOccurrence
    ObservationPeriod --> DrugExposure
    ObservationPeriod --> ProcedureOccurrence
    ObservationPeriod --> Measurement
    ObservationPeriod --> Observation
    ObservationPeriod --> DeviceExposure
```

## Core Tables for FHIR-to-OMOP Mapping

When mapping FHIR resources to OMOP CDM, the following tables are most commonly used:

| FHIR Resource | OMOP Table |
|---------------|------------|
| Patient | PERSON |
| Encounter | VISIT_OCCURRENCE |
| Condition | CONDITION_OCCURRENCE |
| MedicationRequest | DRUG_EXPOSURE |
| Procedure | PROCEDURE_OCCURRENCE |
| Observation (lab) | MEASUREMENT |
| Observation (other) | OBSERVATION |
| Device | DEVICE_EXPOSURE |

## Data Types and Conventions

The OMOP CDM uses the following data types and conventions:

### Primary Keys

- All tables have a primary key column named `[table_name]_id`
- Primary keys are integers
- Primary keys are not meaningful outside the context of the database

### Foreign Keys

- Foreign keys reference primary keys in other tables
- Foreign keys are named after the referenced table (e.g., `person_id`, `visit_occurrence_id`)

### Dates and Times

- Date columns are named with a `_date` suffix (e.g., `birth_date`, `start_date`)
- Datetime columns are named with a `_datetime` suffix (e.g., `birth_datetime`, `start_datetime`)
- Both date and datetime columns may be present for the same event

### Concepts

- Concept columns are named with a `_concept_id` suffix (e.g., `gender_concept_id`)
- Source values are stored in columns with a `_source_value` suffix (e.g., `gender_source_value`)
- Source concept IDs are stored in columns with a `_source_concept_id` suffix (e.g., `gender_source_concept_id`)

## References

1. [OHDSI Common Data Model](https://github.com/OHDSI/CommonDataModel)
2. [OMOP CDM Documentation](https://ohdsi.github.io/CommonDataModel/)
3. [The Book of OHDSI: Chapter 4 - The Common Data Model](https://ohdsi.github.io/TheBookOfOhdsi/CommonDataModel.html)
4. [OMOP CDM v5.4 Specifications](https://ohdsi.github.io/CommonDataModel/cdm54.html)
