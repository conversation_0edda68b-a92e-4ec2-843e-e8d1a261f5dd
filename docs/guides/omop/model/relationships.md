# OMOP CDM Relationships

This document describes the relationships between tables in the OMOP Common Data Model (CDM) v5.4. Understanding these relationships is essential for implementing the OMOP module and mapping FHIR resources to OMOP tables.

## Core Relationships

The OMOP CDM is centered around the Person table, with all clinical events linked to a person. The following diagram illustrates the core relationships:

```mermaid
graph TD
    Person --> ObservationPeriod
    Person --> VisitOccurrence
    Person --> ConditionOccurrence
    Person --> DrugExposure
    Person --> ProcedureOccurrence
    Person --> Measurement
    Person --> Observation
    Person --> DeviceExposure
    
    VisitOccurrence --> ConditionOccurrence
    VisitOccurrence --> DrugExposure
    VisitOccurrence --> ProcedureOccurrence
    VisitOccurrence --> Measurement
    VisitOccurrence --> Observation
    VisitOccurrence --> DeviceExposure
```

## Person-Centric Model

The Person table is the central table in the OMOP CDM. All clinical events are linked to a person through the `person_id` foreign key. This ensures that all data is associated with a specific patient.

### Person to Clinical Events

| Relationship | Description |
|--------------|-------------|
| Person → Observation_Period | A person can have multiple observation periods |
| Person → Visit_Occurrence | A person can have multiple visits |
| Person → Condition_Occurrence | A person can have multiple conditions |
| Person → Drug_Exposure | A person can be exposed to multiple drugs |
| Person → Procedure_Occurrence | A person can undergo multiple procedures |
| Person → Measurement | A person can have multiple measurements |
| Person → Observation | A person can have multiple observations |
| Person → Device_Exposure | A person can be exposed to multiple devices |

## Visit-Based Model

The Visit_Occurrence table represents encounters with healthcare providers or facilities. Clinical events can be linked to a visit through the `visit_occurrence_id` foreign key.

### Visit to Clinical Events

| Relationship | Description |
|--------------|-------------|
| Visit_Occurrence → Condition_Occurrence | A visit can have multiple conditions |
| Visit_Occurrence → Drug_Exposure | A visit can have multiple drug exposures |
| Visit_Occurrence → Procedure_Occurrence | A visit can have multiple procedures |
| Visit_Occurrence → Measurement | A visit can have multiple measurements |
| Visit_Occurrence → Observation | A visit can have multiple observations |
| Visit_Occurrence → Device_Exposure | A visit can have multiple device exposures |

## Provider and Care Site Relationships

The Provider and Care_Site tables represent healthcare providers and facilities. They can be linked to persons, visits, and clinical events.

```mermaid
graph TD
    Provider --> Person
    Provider --> VisitOccurrence
    Provider --> ConditionOccurrence
    Provider --> DrugExposure
    Provider --> ProcedureOccurrence
    Provider --> Measurement
    Provider --> Observation
    
    CareSite --> Person
    CareSite --> VisitOccurrence
    CareSite --> Provider
```

### Provider and Care Site Relationships

| Relationship | Description |
|--------------|-------------|
| Provider → Person | A provider can be associated with multiple persons |
| Provider → Visit_Occurrence | A provider can be associated with multiple visits |
| Provider → Clinical Events | A provider can be associated with multiple clinical events |
| Care_Site → Person | A care site can be associated with multiple persons |
| Care_Site → Visit_Occurrence | A care site can be associated with multiple visits |
| Care_Site → Provider | A care site can be associated with multiple providers |

## Location Relationships

The Location table represents geographic locations. It can be linked to persons and care sites.

```mermaid
graph TD
    Location --> Person
    Location --> CareSite
```

### Location Relationships

| Relationship | Description |
|--------------|-------------|
| Location → Person | A location can be associated with multiple persons |
| Location → Care_Site | A location can be associated with multiple care sites |

## Vocabulary Relationships

The vocabulary tables define the standardized concepts and relationships used in the OMOP CDM. They are linked to the clinical tables through concept IDs.

```mermaid
graph TD
    Concept --> Person
    Concept --> VisitOccurrence
    Concept --> ConditionOccurrence
    Concept --> DrugExposure
    Concept --> ProcedureOccurrence
    Concept --> Measurement
    Concept --> Observation
    Concept --> DeviceExposure
    
    ConceptRelationship --> Concept
    ConceptAncestor --> Concept
    Vocabulary --> Concept
    Domain --> Concept
    ConceptClass --> Concept
```

### Vocabulary Relationships

| Relationship | Description |
|--------------|-------------|
| Concept → Clinical Tables | Concepts are used to standardize data in clinical tables |
| Concept_Relationship → Concept | Defines relationships between concepts |
| Concept_Ancestor → Concept | Defines hierarchical relationships between concepts |
| Vocabulary → Concept | Defines the vocabulary from which a concept is derived |
| Domain → Concept | Defines the domain to which a concept belongs |
| Concept_Class → Concept | Defines the class to which a concept belongs |

## Era Relationships

The era tables (Condition_Era, Drug_Era, Dose_Era) are derived from the clinical tables. They represent continuous periods of a condition or drug exposure.

```mermaid
graph TD
    Person --> ConditionEra
    Person --> DrugEra
    Person --> DoseEra
    
    ConditionOccurrence --> ConditionEra
    DrugExposure --> DrugEra
    DrugExposure --> DoseEra
```

### Era Relationships

| Relationship | Description |
|--------------|-------------|
| Person → Condition_Era | A person can have multiple condition eras |
| Person → Drug_Era | A person can have multiple drug eras |
| Person → Dose_Era | A person can have multiple dose eras |
| Condition_Occurrence → Condition_Era | Condition occurrences are used to derive condition eras |
| Drug_Exposure → Drug_Era | Drug exposures are used to derive drug eras |
| Drug_Exposure → Dose_Era | Drug exposures are used to derive dose eras |

## Foreign Key Constraints

The OMOP CDM defines foreign key constraints to ensure referential integrity. The following table lists the key foreign key constraints:

| Table | Column | References |
|-------|--------|------------|
| Person | gender_concept_id | Concept.concept_id |
| Person | race_concept_id | Concept.concept_id |
| Person | ethnicity_concept_id | Concept.concept_id |
| Person | location_id | Location.location_id |
| Person | provider_id | Provider.provider_id |
| Person | care_site_id | Care_Site.care_site_id |
| Observation_Period | person_id | Person.person_id |
| Observation_Period | period_type_concept_id | Concept.concept_id |
| Visit_Occurrence | person_id | Person.person_id |
| Visit_Occurrence | visit_concept_id | Concept.concept_id |
| Visit_Occurrence | visit_type_concept_id | Concept.concept_id |
| Visit_Occurrence | provider_id | Provider.provider_id |
| Visit_Occurrence | care_site_id | Care_Site.care_site_id |
| Condition_Occurrence | person_id | Person.person_id |
| Condition_Occurrence | condition_concept_id | Concept.concept_id |
| Condition_Occurrence | condition_type_concept_id | Concept.concept_id |
| Condition_Occurrence | provider_id | Provider.provider_id |
| Condition_Occurrence | visit_occurrence_id | Visit_Occurrence.visit_occurrence_id |
| Drug_Exposure | person_id | Person.person_id |
| Drug_Exposure | drug_concept_id | Concept.concept_id |
| Drug_Exposure | drug_type_concept_id | Concept.concept_id |
| Drug_Exposure | provider_id | Provider.provider_id |
| Drug_Exposure | visit_occurrence_id | Visit_Occurrence.visit_occurrence_id |
| Measurement | person_id | Person.person_id |
| Measurement | measurement_concept_id | Concept.concept_id |
| Measurement | measurement_type_concept_id | Concept.concept_id |
| Measurement | provider_id | Provider.provider_id |
| Measurement | visit_occurrence_id | Visit_Occurrence.visit_occurrence_id |

## Implementing Relationships in SQLAlchemy

When implementing the OMOP CDM in SQLAlchemy, relationships are defined using the `relationship` function. Here's an example of how to define relationships for the Person table:

```python
class Person(Base):
    __tablename__ = 'person'
    
    person_id = Column(Integer, primary_key=True)
    gender_concept_id = Column(Integer, ForeignKey('concept.concept_id'), nullable=False)
    year_of_birth = Column(Integer, nullable=False)
    month_of_birth = Column(Integer)
    day_of_birth = Column(Integer)
    birth_datetime = Column(DateTime)
    race_concept_id = Column(Integer, ForeignKey('concept.concept_id'), nullable=False)
    ethnicity_concept_id = Column(Integer, ForeignKey('concept.concept_id'), nullable=False)
    location_id = Column(Integer, ForeignKey('location.location_id'))
    provider_id = Column(Integer, ForeignKey('provider.provider_id'))
    care_site_id = Column(Integer, ForeignKey('care_site.care_site_id'))
    person_source_value = Column(String)
    gender_source_value = Column(String)
    gender_source_concept_id = Column(Integer, ForeignKey('concept.concept_id'))
    race_source_value = Column(String)
    race_source_concept_id = Column(Integer, ForeignKey('concept.concept_id'))
    ethnicity_source_value = Column(String)
    ethnicity_source_concept_id = Column(Integer, ForeignKey('concept.concept_id'))
    
    # Relationships
    gender_concept = relationship("Concept", foreign_keys=[gender_concept_id])
    race_concept = relationship("Concept", foreign_keys=[race_concept_id])
    ethnicity_concept = relationship("Concept", foreign_keys=[ethnicity_concept_id])
    location = relationship("Location")
    provider = relationship("Provider")
    care_site = relationship("CareSite")
    
    observation_periods = relationship("ObservationPeriod", back_populates="person")
    visit_occurrences = relationship("VisitOccurrence", back_populates="person")
    condition_occurrences = relationship("ConditionOccurrence", back_populates="person")
    drug_exposures = relationship("DrugExposure", back_populates="person")
    procedure_occurrences = relationship("ProcedureOccurrence", back_populates="person")
    measurements = relationship("Measurement", back_populates="person")
    observations = relationship("Observation", back_populates="person")
    device_exposures = relationship("DeviceExposure", back_populates="person")
```

## References

1. [OHDSI Common Data Model](https://github.com/OHDSI/CommonDataModel)
2. [OMOP CDM Documentation](https://ohdsi.github.io/CommonDataModel/)
3. [OMOP CDM v5.4 Specifications](https://ohdsi.github.io/CommonDataModel/cdm54.html)
4. [SQLAlchemy Documentation](https://docs.sqlalchemy.org/)
