# OMOP CDM Table Descriptions

This document provides detailed descriptions of the key tables in the OMOP Common Data Model (CDM) v5.4. For a complete list of tables and their specifications, refer to the [OMOP CDM Documentation](https://ohdsi.github.io/CommonDataModel/).

## Clinical Data Tables

### PERSON

The Person table contains records that uniquely identify each patient in the source data.

| Column | Description | Data Type | Required |
|--------|-------------|-----------|----------|
| person_id | A unique identifier for each person. | Integer | Yes |
| gender_concept_id | A foreign key that refers to a standard concept identifier in the Vocabulary for the gender of the person. | Integer | Yes |
| year_of_birth | The year of birth of the person. | Integer | Yes |
| month_of_birth | The month of birth of the person. | Integer | No |
| day_of_birth | The day of birth of the person. | Integer | No |
| birth_datetime | The date and time of birth of the person. | Datetime | No |
| race_concept_id | A foreign key that refers to a standard concept identifier in the Vocabulary for the race of the person. | Integer | Yes |
| ethnicity_concept_id | A foreign key that refers to a standard concept identifier in the Vocabulary for the ethnicity of the person. | Integer | Yes |
| location_id | A foreign key to the Location table, where the details of the address are stored. | Integer | No |
| provider_id | A foreign key to the Provider table, where the details of the provider are stored. | Integer | No |
| care_site_id | A foreign key to the Care Site table, where the details of the care site are stored. | Integer | No |
| person_source_value | The source value for the person identifier. | String | No |
| gender_source_value | The source value for the gender of the person. | String | No |
| gender_source_concept_id | A foreign key to the concept that refers to the code used in the source. | Integer | No |
| race_source_value | The source value for the race of the person. | String | No |
| race_source_concept_id | A foreign key to the concept that refers to the code used in the source. | Integer | No |
| ethnicity_source_value | The source value for the ethnicity of the person. | String | No |
| ethnicity_source_concept_id | A foreign key to the concept that refers to the code used in the source. | Integer | No |

### OBSERVATION_PERIOD

The Observation Period table contains records that define the spans of time during which a person is observed.

| Column | Description | Data Type | Required |
|--------|-------------|-----------|----------|
| observation_period_id | A unique identifier for each observation period. | Integer | Yes |
| person_id | A foreign key identifier to the Person who is being observed. | Integer | Yes |
| observation_period_start_date | The start date of the observation period. | Date | Yes |
| observation_period_end_date | The end date of the observation period. | Date | Yes |
| period_type_concept_id | A foreign key that refers to the type of observation period. | Integer | Yes |

### VISIT_OCCURRENCE

The Visit Occurrence table contains records of encounters with healthcare providers or facilities.

| Column | Description | Data Type | Required |
|--------|-------------|-----------|----------|
| visit_occurrence_id | A unique identifier for each visit. | Integer | Yes |
| person_id | A foreign key identifier to the Person for whom the visit is recorded. | Integer | Yes |
| visit_concept_id | A foreign key that refers to a standard concept identifier in the Vocabulary for the visit type. | Integer | Yes |
| visit_start_date | The start date of the visit. | Date | Yes |
| visit_start_datetime | The start date and time of the visit. | Datetime | No |
| visit_end_date | The end date of the visit. | Date | Yes |
| visit_end_datetime | The end date and time of the visit. | Datetime | No |
| visit_type_concept_id | A foreign key that refers to the type of visit. | Integer | Yes |
| provider_id | A foreign key to the Provider table, where the details of the provider are stored. | Integer | No |
| care_site_id | A foreign key to the Care Site table, where the details of the care site are stored. | Integer | No |
| visit_source_value | The source value for the visit. | String | No |
| visit_source_concept_id | A foreign key to the concept that refers to the code used in the source. | Integer | No |
| admitted_from_concept_id | A foreign key that refers to the concept of the place from which the patient was admitted. | Integer | No |
| admitted_from_source_value | The source value for the place from which the patient was admitted. | String | No |
| discharge_to_concept_id | A foreign key that refers to the concept of the place to which the patient was discharged. | Integer | No |
| discharge_to_source_value | The source value for the place to which the patient was discharged. | String | No |
| preceding_visit_occurrence_id | A foreign key to the Visit Occurrence table for the immediately preceding visit. | Integer | No |

### CONDITION_OCCURRENCE

The Condition Occurrence table contains records of conditions or diagnoses.

| Column | Description | Data Type | Required |
|--------|-------------|-----------|----------|
| condition_occurrence_id | A unique identifier for each condition occurrence. | Integer | Yes |
| person_id | A foreign key identifier to the Person who has the condition. | Integer | Yes |
| condition_concept_id | A foreign key that refers to a standard concept identifier in the Vocabulary for the condition. | Integer | Yes |
| condition_start_date | The date when the condition was first recorded. | Date | Yes |
| condition_start_datetime | The date and time when the condition was first recorded. | Datetime | No |
| condition_end_date | The date when the condition was resolved. | Date | No |
| condition_end_datetime | The date and time when the condition was resolved. | Datetime | No |
| condition_type_concept_id | A foreign key that refers to the type of condition. | Integer | Yes |
| condition_status_concept_id | A foreign key that refers to the status of the condition. | Integer | No |
| stop_reason | The reason the condition was no longer present. | String | No |
| provider_id | A foreign key to the Provider table, where the details of the provider are stored. | Integer | No |
| visit_occurrence_id | A foreign key to the Visit Occurrence table, where the details of the visit are stored. | Integer | No |
| visit_detail_id | A foreign key to the Visit Detail table, where the details of the visit are stored. | Integer | No |
| condition_source_value | The source value for the condition. | String | No |
| condition_source_concept_id | A foreign key to the concept that refers to the code used in the source. | Integer | No |
| condition_status_source_value | The source value for the condition status. | String | No |

### DRUG_EXPOSURE

The Drug Exposure table contains records of drugs or medications.

| Column | Description | Data Type | Required |
|--------|-------------|-----------|----------|
| drug_exposure_id | A unique identifier for each drug exposure. | Integer | Yes |
| person_id | A foreign key identifier to the Person who is exposed to the drug. | Integer | Yes |
| drug_concept_id | A foreign key that refers to a standard concept identifier in the Vocabulary for the drug. | Integer | Yes |
| drug_exposure_start_date | The start date of the drug exposure. | Date | Yes |
| drug_exposure_start_datetime | The start date and time of the drug exposure. | Datetime | No |
| drug_exposure_end_date | The end date of the drug exposure. | Date | No |
| drug_exposure_end_datetime | The end date and time of the drug exposure. | Datetime | No |
| verbatim_end_date | The verbatim end date of the drug exposure. | Date | No |
| drug_type_concept_id | A foreign key that refers to the type of drug exposure. | Integer | Yes |
| stop_reason | The reason the drug was stopped. | String | No |
| refills | The number of refills after the initial prescription. | Integer | No |
| quantity | The quantity of drug as recorded in the source. | Float | No |
| days_supply | The number of days of supply of the medication. | Integer | No |
| sig | The directions on the drug prescription. | String | No |
| route_concept_id | A foreign key that refers to the route of administration. | Integer | No |
| lot_number | The lot number of the drug. | String | No |
| provider_id | A foreign key to the Provider table, where the details of the provider are stored. | Integer | No |
| visit_occurrence_id | A foreign key to the Visit Occurrence table, where the details of the visit are stored. | Integer | No |
| visit_detail_id | A foreign key to the Visit Detail table, where the details of the visit are stored. | Integer | No |
| drug_source_value | The source value for the drug. | String | No |
| drug_source_concept_id | A foreign key to the concept that refers to the code used in the source. | Integer | No |
| route_source_value | The source value for the route of administration. | String | No |
| dose_unit_source_value | The source value for the dose unit. | String | No |

### MEASUREMENT

The Measurement table contains records of measurements or observations that have a value.

| Column | Description | Data Type | Required |
|--------|-------------|-----------|----------|
| measurement_id | A unique identifier for each measurement. | Integer | Yes |
| person_id | A foreign key identifier to the Person for whom the measurement is recorded. | Integer | Yes |
| measurement_concept_id | A foreign key that refers to a standard concept identifier in the Vocabulary for the measurement. | Integer | Yes |
| measurement_date | The date of the measurement. | Date | Yes |
| measurement_datetime | The date and time of the measurement. | Datetime | No |
| measurement_time | The time of the measurement. | String | No |
| measurement_type_concept_id | A foreign key that refers to the type of measurement. | Integer | Yes |
| operator_concept_id | A foreign key that refers to the operator of the measurement. | Integer | No |
| value_as_number | The numeric value of the measurement. | Float | No |
| value_as_concept_id | A foreign key that refers to a concept identifier for the value of the measurement. | Integer | No |
| unit_concept_id | A foreign key that refers to a concept identifier for the unit of the measurement. | Integer | No |
| range_low | The lower limit of the normal range of the measurement. | Float | No |
| range_high | The upper limit of the normal range of the measurement. | Float | No |
| provider_id | A foreign key to the Provider table, where the details of the provider are stored. | Integer | No |
| visit_occurrence_id | A foreign key to the Visit Occurrence table, where the details of the visit are stored. | Integer | No |
| visit_detail_id | A foreign key to the Visit Detail table, where the details of the visit are stored. | Integer | No |
| measurement_source_value | The source value for the measurement. | String | No |
| measurement_source_concept_id | A foreign key to the concept that refers to the code used in the source. | Integer | No |
| unit_source_value | The source value for the unit of the measurement. | String | No |
| value_source_value | The source value for the value of the measurement. | String | No |

## Vocabulary Tables

### CONCEPT

The Concept table contains records that uniquely identify each concept in the vocabulary.

| Column | Description | Data Type | Required |
|--------|-------------|-----------|----------|
| concept_id | A unique identifier for each concept. | Integer | Yes |
| concept_name | The name of the concept. | String | Yes |
| domain_id | The domain of the concept. | String | Yes |
| vocabulary_id | The vocabulary from which the concept is derived. | String | Yes |
| concept_class_id | The class of the concept. | String | Yes |
| standard_concept | Indicates whether the concept is standard (S), classification (C), or non-standard (null). | String | No |
| concept_code | The code for the concept in the source vocabulary. | String | Yes |
| valid_start_date | The date when the concept was first valid. | Date | Yes |
| valid_end_date | The date when the concept became invalid. | Date | Yes |
| invalid_reason | The reason the concept became invalid. | String | No |

### VOCABULARY

The Vocabulary table includes a list of the vocabularies collected from various sources.

| Column | Description | Data Type | Required |
|--------|-------------|-----------|----------|
| vocabulary_id | A unique identifier for each vocabulary. | String | Yes |
| vocabulary_name | The name of the vocabulary. | String | Yes |
| vocabulary_reference | The reference to the vocabulary source. | String | No |
| vocabulary_version | The version of the vocabulary. | String | No |
| vocabulary_concept_id | A foreign key that refers to a concept that represents the vocabulary. | Integer | Yes |

### DOMAIN

The Domain table includes a list of domains that are used to categorize concepts.

| Column | Description | Data Type | Required |
|--------|-------------|-----------|----------|
| domain_id | A unique identifier for each domain. | String | Yes |
| domain_name | The name of the domain. | String | Yes |
| domain_concept_id | A foreign key that refers to a concept that represents the domain. | Integer | Yes |

### CONCEPT_RELATIONSHIP

The Concept Relationship table defines relationships between concepts.

| Column | Description | Data Type | Required |
|--------|-------------|-----------|----------|
| concept_id_1 | A foreign key to the first concept. | Integer | Yes |
| concept_id_2 | A foreign key to the second concept. | Integer | Yes |
| relationship_id | A foreign key to the relationship. | String | Yes |
| valid_start_date | The date when the relationship was first valid. | Date | Yes |
| valid_end_date | The date when the relationship became invalid. | Date | Yes |
| invalid_reason | The reason the relationship became invalid. | String | No |

## References

1. [OHDSI Common Data Model](https://github.com/OHDSI/CommonDataModel)
2. [OMOP CDM Documentation](https://ohdsi.github.io/CommonDataModel/)
3. [OMOP CDM v5.4 Specifications](https://ohdsi.github.io/CommonDataModel/cdm54.html)
