# OMOP Module Implementation Roadmap

This document outlines the phased approach to implementing the OMOP module, focusing on incremental development and testing.

## Development Workflow Visualization

The following diagram illustrates the incremental development workflow for a single developer implementing the OMOP module:

```mermaid
%%{init: {'theme': 'neutral'}}%%
graph TD
    %% Main phases
    Start([Start]) --> DB[Database Foundation]
    DB --> Models[Core Models]
    Models --> DAOs[Basic Data Access]
    DAOs --> Vocab[Vocabulary Management]
    Vocab --> BaseMapper[Base Mapper Framework]
    BaseMapper --> CoreMappers[Core Mappers]
    CoreMappers --> Integration[FHIR Integration]
    Integration --> ExtendedMappers[Extended Mappers]
    ExtendedMappers --> Testing[Testing & Validation]
    Testing --> Documentation[Documentation]
    Documentation --> End([Complete])

    %% Subcomponents and testing cycles
    subgraph "Iterative Testing Cycles"
        DB --> TestDB[Test DB Setup]
        Models --> TestModels[Test Models]
        DAOs --> TestDAOs[Test DAOs]
        Vocab --> TestVocab[Test Vocabulary]
        BaseMapper --> TestBaseMapper[Test Base Mapper]
        CoreMappers --> TestCoreMappers[Test Core Mappers]
        Integration --> TestIntegration[Test Integration]
        ExtendedMappers --> TestExtendedMappers[Test Extended Mappers]
    end

    %% Simple styling with default colors
    classDef phase stroke-width:2px;
    classDef test stroke-width:1px;
    classDef milestone stroke-width:2px;

    class DB,Models,DAOs,Vocab,BaseMapper,CoreMappers,Integration,ExtendedMappers,Testing,Documentation phase;
    class TestDB,TestModels,TestDAOs,TestVocab,TestBaseMapper,TestCoreMappers,TestIntegration,TestExtendedMappers test;
    class Start,End milestone;
```

This workflow emphasizes:
- **Incremental Development**: Building one component at a time
- **Test-Driven Approach**: Testing each component before moving to the next
- **Logical Dependencies**: Following a sequence that minimizes rework
- **Manageable Chunks**: Breaking down the work into achievable tasks for a single developer

## Phase 2: OMOP Module Development

The OMOP module development (Phase 2) follows the completion of the FHIR module (Phase 1) and precedes the ETL implementation (Phase 3). This phase focuses on creating the foundational OMOP database structure and basic functionality.

### Phase 2.1: Foundation

**Objective**: Establish the basic OMOP database structure and core components.

#### Tasks:

1. **Database Setup**
   - Implement database connection management for PostgreSQL and SQLite
   - Create schema creation scripts based on OMOP CDM v5.4
   - Implement schema validation utilities

2. **Core Models**
   - Implement SQLAlchemy models for OMOP CDM tables
   - Define relationships between models
   - Implement validation for model constraints

3. **Vocabulary Management**
   - Create vocabulary loading utilities
   - Implement vocabulary validation
   - Set up basic concept lookup functionality

4. **Basic Data Access**
   - Implement base Data Access Object (DAO)
   - Create DAOs for core tables (Person, Visit_Occurrence)
   - Implement transaction management

#### Deliverables:

- Functional OMOP database with core tables
- SQLAlchemy models for OMOP CDM
- Basic vocabulary management
- Core data access layer

### Phase 2.2: Mapping Framework

**Objective**: Develop the framework for mapping FHIR resources to OMOP tables.

#### Tasks:

1. **Base Mapper**
   - Implement base mapper with common functionality
   - Create mapping validation utilities
   - Implement error handling and logging

2. **Vocabulary Service**
   - Enhance concept lookup functionality
   - Implement concept mapping utilities
   - Create caching for frequently used concepts

3. **Core Mappers**
   - Implement Patient to Person mapper
   - Implement Encounter to Visit_Occurrence mapper
   - Create test cases for core mappers

4. **Configuration**
   - Enhance configuration for OMOP module
   - Implement mapping configuration
   - Create environment variable templates

#### Deliverables:

- Base mapper framework
- Enhanced vocabulary service
- Core mappers for Patient and Encounter
- Configuration for OMOP module

### Phase 2.3: Extended Functionality

**Objective**: Extend the OMOP module with additional functionality and mappers.

#### Tasks:

1. **Additional Mappers**
   - Implement Condition to Condition_Occurrence mapper
   - Implement Observation to Measurement/Observation mapper
   - Implement MedicationRequest to Drug_Exposure mapper
   - Implement Procedure to Procedure_Occurrence mapper

2. **Advanced Vocabulary Functionality**
   - Implement concept relationship navigation
   - Create utilities for domain-specific concept lookup
   - Enhance caching for performance

3. **Query Utilities**
   - Implement common query patterns
   - Create utilities for cohort definition
   - Implement data extraction for analysis

4. **Validation and Quality Checks**
   - Implement data validation utilities
   - Create data quality check framework
   - Implement reporting for validation results

#### Deliverables:

- Complete set of core mappers
- Advanced vocabulary functionality
- Query utilities for common operations
- Validation and quality check framework

### Phase 2.4: Integration and Testing

**Objective**: Integrate the OMOP module with the FHIR module and perform comprehensive testing.

#### Tasks:

1. **Integration**
   - Integrate OMOP module with FHIR module
   - Implement shared configuration
   - Create integration tests

2. **End-to-End Testing**
   - Test complete FHIR-to-OMOP transformation
   - Validate data integrity across the pipeline
   - Measure and optimize performance

3. **Documentation**
   - Complete module documentation
   - Create usage examples
   - Document best practices

4. **Refinement**
   - Address issues identified during testing
   - Optimize performance bottlenecks
   - Refine error handling and logging

#### Deliverables:

- Integrated FHIR-to-OMOP pipeline
- Comprehensive test suite
- Complete documentation
- Optimized and refined module

## Phase 3: ETL Implementation (Future)

After completing Phase 2, the project will move to Phase 3, which focuses on implementing the ETL process for transforming FHIR data to OMOP CDM. This phase will build on the foundation established in Phase 2.

### Key Components for Phase 3:

1. **ETL Orchestration**
   - Workflow management
   - Job scheduling
   - Error handling and retry mechanisms

2. **Batch Processing**
   - Efficient processing of large datasets
   - Parallel processing
   - Checkpointing and resumability

3. **Incremental Updates**
   - Delta detection
   - Incremental loading
   - Change data capture

4. **Monitoring and Reporting**
   - Performance monitoring
   - Data quality reporting
   - Transformation metrics

## Detailed Task Sequence

The following diagram shows a detailed sequence of tasks for each phase, providing a clear roadmap for a single developer:

```mermaid
%%{init: {'theme': 'neutral'}}%%
gantt
    title OMOP Module Implementation Sequence
    dateFormat  YYYY-MM-DD
    axisFormat %d-%m

    section Phase 2.1: Foundation
    Database Connection Management    :a1, 2023-01-01, 3d
    Schema Creation Scripts           :a2, after a1, 3d
    Core SQLAlchemy Models           :a3, after a2, 5d
    Model Relationships              :a4, after a3, 3d
    Basic Vocabulary Loading         :a5, after a4, 4d
    Unit Tests for Foundation        :a6, after a5, 3d

    section Phase 2.2: Mapping Framework
    Base DAO Implementation          :b1, after a6, 4d
    Person & Visit DAOs              :b2, after b1, 3d
    Concept Service Basic            :b3, after b2, 4d
    Base Mapper Framework            :b4, after b3, 5d
    Patient & Encounter Mappers      :b5, after b4, 5d
    Unit Tests for Mappers           :b6, after b5, 3d

    section Phase 2.3: Extended Functionality
    Condition & Observation Mappers  :c1, after b6, 5d
    Medication & Procedure Mappers   :c2, after c1, 5d
    Advanced Vocabulary Functions    :c3, after c2, 4d
    Query Utilities                  :c4, after c3, 3d
    Validation Framework             :c5, after c4, 4d
    Unit Tests for Extensions        :c6, after c5, 3d

    section Phase 2.4: Integration & Testing
    FHIR Module Integration          :d1, after c6, 5d
    End-to-End Pipeline              :d2, after d1, 4d
    Performance Optimization         :d3, after d2, 3d
    Integration Tests                :d4, after d3, 4d
    Documentation Updates            :d5, after d4, 3d
    Final Review & Refinement        :d6, after d5, 3d
```

This Gantt chart provides:
- A logical sequence of tasks
- Dependencies between tasks
- Estimated duration for each task
- Clear milestones for testing and validation

## Implementation Timeline

The following timeline provides a rough estimate for the implementation of Phase 2:

| Phase | Duration | Description | Key Deliverables |
|-------|----------|-------------|------------------|
| 2.1   | 2-3 weeks | Foundation | Database connection, schema, core models, basic vocabulary |
| 2.2   | 2-3 weeks | Mapping Framework | DAOs, concept service, base mapper, core mappers |
| 2.3   | 3-4 weeks | Extended Functionality | Additional mappers, advanced vocabulary, validation |
| 2.4   | 2-3 weeks | Integration and Testing | FHIR integration, end-to-end testing, documentation |
| Total | 9-13 weeks | Complete OMOP Module | Fully functional OMOP module integrated with FHIR |

## Component Dependencies and Data Flow

The following diagram illustrates the dependencies between components and the flow of data through the system:

```mermaid
%%{init: {'theme': 'neutral'}}%%
flowchart TD
    %% Data sources
    FHIR[FHIR Server] --> |Extract| Resources[FHIR Resources]

    %% FHIR Module components
    Resources --> Parser[Resource Parser]
    Parser --> |Parsed Objects| Mapper[FHIR-to-OMOP Mappers]

    %% OMOP Module components
    subgraph "OMOP Module"
        Mapper --> |Transform| DAO[Data Access Objects]
        VocabService[Vocabulary Service] --> |Concept Mapping| Mapper
        DAO --> |Store| DB[(OMOP Database)]
        VocabService --> |Query| DB
    end

    %% Database components
    subgraph "Database Layer"
        DB --> |Contains| Clinical[Clinical Tables]
        DB --> |Contains| Vocab[Vocabulary Tables]
        DB --> |Contains| Meta[Metadata Tables]
    end

    %% Support components
    Config[Configuration] --> Mapper
    Config --> DAO
    Config --> VocabService

    Validation[Validation Service] --> Mapper
    Validation --> DAO

    %% Simple styling with default colors
    classDef fhir stroke-width:1px;
    classDef omop stroke-width:1px;
    classDef db stroke-width:1px;
    classDef support stroke-width:1px;

    class FHIR,Resources,Parser fhir;
    class Mapper,DAO,VocabService omop;
    class DB,Clinical,Vocab,Meta db;
    class Config,Validation support;
```

This diagram shows:
- The data flow from the FHIR server to the OMOP database
- The dependencies between different components
- The clear separation between FHIR and OMOP modules
- The support components that facilitate integration

## Development Prioritization

For a single developer, the following prioritization sequence is recommended:

1. **First the foundation**: Set up the OMOP database and basic models
2. **Then the infrastructure**: Implement DAOs and vocabulary service
3. **Next the mappers**: Develop mappers starting with the most fundamental ones (Patient, Encounter)
4. **Finally the integration**: Connect with the existing FHIR module

At each stage, follow this cycle:
1. Implement a minimum viable component
2. Write tests to validate its functionality
3. Refine as needed
4. Document before moving to the next component

## Success Criteria

The successful completion of Phase 2 will be determined by the following criteria:

1. **Functional OMOP Database**: Properly configured OMOP CDM database with core tables
2. **Complete Data Model**: SQLAlchemy models for all OMOP CDM tables with proper relationships
3. **Vocabulary Management**: Ability to load and query OMOP vocabularies
4. **Core Mappers**: Functional mappers for key FHIR resources
5. **Integration**: Successful integration with the FHIR module
6. **Documentation**: Comprehensive documentation for the OMOP module
7. **Tests**: Comprehensive test suite with high coverage
8. **Performance**: Acceptable performance for typical data volumes
9. **Usability**: Clear interfaces and workflows for developers

## References

1. [HL7 Vulcan FHIR-to-OMOP Implementation Guide](https://build.fhir.org/ig/HL7/fhir-omop-ig/)
2. [OHDSI Common Data Model](https://github.com/OHDSI/CommonDataModel)
3. [OMOP CDM Documentation](https://ohdsi.github.io/CommonDataModel/)
4. [The Book of OHDSI](https://ohdsi.github.io/TheBookOfOhdsi/)
