# OMOP Vocabulary Loading Guide

This guide explains how to load OMOP vocabularies into your OMOP CDM database. Before proceeding, ensure you have downloaded the vocabularies from Athena as described in the [Athena Setup](athena_setup.md) guide.

## Prerequisites

- OMOP CDM database set up (PostgreSQL or SQLite)
- Vocabulary files downloaded from Athena
- Python environment with required packages

## Vocabulary Files

After downloading and extracting vocabularies from Athena, you should have the following files:

| File | Description |
|------|-------------|
| `CONCEPT.csv` | All concepts across all vocabularies |
| `VOCABULARY.csv` | Information about the vocabularies |
| `DOMAIN.csv` | Domains for concepts |
| `CONCEPT_CLASS.csv` | Classes for concepts |
| `CONCEPT_RELATIONSHIP.csv` | Relationships between concepts |
| `RELATIONSHIP.csv` | Types of relationships |
| `CONCEPT_SYNONYM.csv` | Alternative names for concepts |
| `CONCEPT_ANCESTOR.csv` | Hierarchical relationships |
| `SOURCE_TO_CONCEPT_MAP.csv` | Mappings from source codes to standard concepts |
| `DRUG_STRENGTH.csv` | Drug strength information |

## Loading Vocabularies into PostgreSQL

### Option 1: Using psql COPY Command (Recommended for Large Vocabularies)

1. **Connect to the database**:
   ```bash
   psql -U <username> -d <database_name>
   ```

2. **Load each vocabulary file**:
   ```sql
   \COPY concept FROM '/path/to/vocabulary/CONCEPT.csv' WITH DELIMITER E'\t' CSV HEADER QUOTE E'\b';
   \COPY vocabulary FROM '/path/to/vocabulary/VOCABULARY.csv' WITH DELIMITER E'\t' CSV HEADER QUOTE E'\b';
   \COPY domain FROM '/path/to/vocabulary/DOMAIN.csv' WITH DELIMITER E'\t' CSV HEADER QUOTE E'\b';
   \COPY concept_class FROM '/path/to/vocabulary/CONCEPT_CLASS.csv' WITH DELIMITER E'\t' CSV HEADER QUOTE E'\b';
   \COPY concept_relationship FROM '/path/to/vocabulary/CONCEPT_RELATIONSHIP.csv' WITH DELIMITER E'\t' CSV HEADER QUOTE E'\b';
   \COPY relationship FROM '/path/to/vocabulary/RELATIONSHIP.csv' WITH DELIMITER E'\t' CSV HEADER QUOTE E'\b';
   \COPY concept_synonym FROM '/path/to/vocabulary/CONCEPT_SYNONYM.csv' WITH DELIMITER E'\t' CSV HEADER QUOTE E'\b';
   \COPY concept_ancestor FROM '/path/to/vocabulary/CONCEPT_ANCESTOR.csv' WITH DELIMITER E'\t' CSV HEADER QUOTE E'\b';
   \COPY source_to_concept_map FROM '/path/to/vocabulary/SOURCE_TO_CONCEPT_MAP.csv' WITH DELIMITER E'\t' CSV HEADER QUOTE E'\b';
   \COPY drug_strength FROM '/path/to/vocabulary/DRUG_STRENGTH.csv' WITH DELIMITER E'\t' CSV HEADER QUOTE E'\b';
   ```

### Option 2: Using Python Script

1. **Create a script** named `load_vocabularies.py`:
   ```python
   import os
   import pandas as pd
   from sqlalchemy import create_engine
   import time

   # Database connection
   engine = create_engine('postgresql://username:password@localhost:5432/omop_cdm')

   # Vocabulary directory
   vocab_dir = '/path/to/vocabulary'

   # Vocabulary tables and their corresponding files
   vocab_tables = {
       'concept': 'CONCEPT.csv',
       'vocabulary': 'VOCABULARY.csv',
       'domain': 'DOMAIN.csv',
       'concept_class': 'CONCEPT_CLASS.csv',
       'concept_relationship': 'CONCEPT_RELATIONSHIP.csv',
       'relationship': 'RELATIONSHIP.csv',
       'concept_synonym': 'CONCEPT_SYNONYM.csv',
       'concept_ancestor': 'CONCEPT_ANCESTOR.csv',
       'source_to_concept_map': 'SOURCE_TO_CONCEPT_MAP.csv',
       'drug_strength': 'DRUG_STRENGTH.csv'
   }

   # Load each vocabulary file
   for table, file_name in vocab_tables.items():
       file_path = os.path.join(vocab_dir, file_name)
       if not os.path.exists(file_path):
           print(f"File not found: {file_path}")
           continue
       
       print(f"Loading {file_name} into {table}...")
       start_time = time.time()
       
       # Read CSV in chunks to avoid memory issues
       chunksize = 100000
       for i, chunk in enumerate(pd.read_csv(file_path, sep='\t', chunksize=chunksize)):
           chunk.to_sql(table, engine, if_exists='append' if i > 0 else 'replace', index=False)
           print(f"  Loaded chunk {i+1} ({chunksize} rows)")
       
       elapsed_time = time.time() - start_time
       print(f"Completed loading {file_name} in {elapsed_time:.2f} seconds")

   print("Vocabulary loading completed")
   ```

2. **Run the script**:
   ```bash
   python load_vocabularies.py
   ```

## Loading Vocabularies into SQLite

### Using Python Script

1. **Create a script** named `load_vocabularies_sqlite.py`:
   ```python
   import os
   import pandas as pd
   import sqlite3
   import time

   # Database connection
   conn = sqlite3.connect('/path/to/omop_cdm.db')

   # Vocabulary directory
   vocab_dir = '/path/to/vocabulary'

   # Vocabulary tables and their corresponding files
   vocab_tables = {
       'concept': 'CONCEPT.csv',
       'vocabulary': 'VOCABULARY.csv',
       'domain': 'DOMAIN.csv',
       'concept_class': 'CONCEPT_CLASS.csv',
       'concept_relationship': 'CONCEPT_RELATIONSHIP.csv',
       'relationship': 'RELATIONSHIP.csv',
       'concept_synonym': 'CONCEPT_SYNONYM.csv',
       'concept_ancestor': 'CONCEPT_ANCESTOR.csv',
       'source_to_concept_map': 'SOURCE_TO_CONCEPT_MAP.csv',
       'drug_strength': 'DRUG_STRENGTH.csv'
   }

   # Load each vocabulary file
   for table, file_name in vocab_tables.items():
       file_path = os.path.join(vocab_dir, file_name)
       if not os.path.exists(file_path):
           print(f"File not found: {file_path}")
           continue
       
       print(f"Loading {file_name} into {table}...")
       start_time = time.time()
       
       # Read CSV in chunks to avoid memory issues
       chunksize = 100000
       for i, chunk in enumerate(pd.read_csv(file_path, sep='\t', chunksize=chunksize)):
           chunk.to_sql(table, conn, if_exists='append' if i > 0 else 'replace', index=False)
           print(f"  Loaded chunk {i+1} ({chunksize} rows)")
       
       elapsed_time = time.time() - start_time
       print(f"Completed loading {file_name} in {elapsed_time:.2f} seconds")

   # Close connection
   conn.close()

   print("Vocabulary loading completed")
   ```

2. **Run the script**:
   ```bash
   python load_vocabularies_sqlite.py
   ```

## Using the Vocabulary Manager

The OMOP module includes a `VocabularyManager` class that simplifies vocabulary loading:

```python
from fhir_omop.omop.db.vocabulary_manager import VocabularyManager
from fhir_omop.omop.db.db_manager import OmopDBManager

# Initialize database manager
db_manager = OmopDBManager('postgresql://username:password@localhost:5432/omop_cdm')

# Initialize vocabulary manager
vocab_manager = VocabularyManager(db_manager)

# Load vocabularies
vocab_manager.load_vocabulary('/path/to/vocabulary')

# Check vocabulary loading
vocab_counts = vocab_manager.check_vocabulary()
for table, count in vocab_counts.items():
    print(f"{table}: {count} records")
```

## Verifying Vocabulary Loading

After loading the vocabularies, verify that they were loaded correctly:

### PostgreSQL

```sql
-- Count records in each vocabulary table
SELECT 'concept' AS table_name, COUNT(*) AS record_count FROM concept
UNION ALL
SELECT 'vocabulary', COUNT(*) FROM vocabulary
UNION ALL
SELECT 'domain', COUNT(*) FROM domain
UNION ALL
SELECT 'concept_class', COUNT(*) FROM concept_class
UNION ALL
SELECT 'concept_relationship', COUNT(*) FROM concept_relationship
UNION ALL
SELECT 'relationship', COUNT(*) FROM relationship
UNION ALL
SELECT 'concept_synonym', COUNT(*) FROM concept_synonym
UNION ALL
SELECT 'concept_ancestor', COUNT(*) FROM concept_ancestor
UNION ALL
SELECT 'source_to_concept_map', COUNT(*) FROM source_to_concept_map
UNION ALL
SELECT 'drug_strength', COUNT(*) FROM drug_strength
ORDER BY table_name;
```

### SQLite

```python
import sqlite3

conn = sqlite3.connect('/path/to/omop_cdm.db')
cursor = conn.cursor()

tables = [
    'concept', 'vocabulary', 'domain', 'concept_class',
    'concept_relationship', 'relationship', 'concept_synonym',
    'concept_ancestor', 'source_to_concept_map', 'drug_strength'
]

for table in tables:
    cursor.execute(f"SELECT COUNT(*) FROM {table}")
    count = cursor.fetchone()[0]
    print(f"{table}: {count} records")

conn.close()
```

## Troubleshooting

### Common Issues

1. **Memory Errors**: When loading large files, you may encounter memory errors. Use chunking as shown in the examples.

2. **Permission Issues**: Ensure the database user has permission to write to the tables.

3. **Disk Space**: Vocabulary files can be large. Ensure you have enough disk space.

4. **Timeout Issues**: Loading large files may take time. Increase timeout settings if necessary.

5. **Encoding Issues**: If you encounter encoding errors, check the file encoding and adjust the loading process accordingly.

## References

1. [OHDSI Vocabulary Loading Documentation](https://ohdsi.github.io/CommonDataModel/vocabulary.html)
2. [The Book of OHDSI: Chapter 4 - The Standardized Vocabularies](https://ohdsi.github.io/TheBookOfOhdsi/StandardizedVocabularies.html)
3. [PostgreSQL COPY Command Documentation](https://www.postgresql.org/docs/current/sql-copy.html)
4. [Pandas to_sql Documentation](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.DataFrame.to_sql.html)
