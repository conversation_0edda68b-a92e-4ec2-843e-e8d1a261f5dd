#!/usr/bin/env python
"""
Script para verificar los vocabularios OMOP combinados.
"""
import pandas as pd
import os
from pathlib import Path

# Ruta a los vocabularios combinados
VOCAB_PATH = Path("data/vocabulary/omop_v5_20250227")

def check_vocabularies():
    """Verificar qué vocabularios están disponibles."""
    vocab_file = VOCAB_PATH / "VOCABULARY.csv"

    if not vocab_file.exists():
        print(f"❌ Archivo no encontrado: {vocab_file}")
        return

    try:
        # Leer el archivo de vocabularios
        df = pd.read_csv(vocab_file, sep='\t')

        # Vocabularios esenciales a verificar
        essential_vocabs = ["SNOMED", "LOINC", "RxNorm", "CPT4", "ICD10CM", "ATC"]

        print("=== Vocabularios Esenciales ===")
        for vocab in essential_vocabs:
            if vocab in df['vocabulary_id'].values:
                version = df[df['vocabulary_id'] == vocab]['vocabulary_version'].values[0]
                print(f"✅ {vocab}: {version}")
            else:
                print(f"❌ {vocab}: No encontrado")

        print("\n=== Todos los Vocabularios ===")
        for _, row in df.iterrows():
            print(f"- {row['vocabulary_id']}: {row['vocabulary_name']}")

        print(f"\nTotal de vocabularios: {len(df)}")

    except Exception as e:
        print(f"❌ Error al verificar vocabularios: {e}")

def count_concepts():
    """Contar conceptos por vocabulario."""
    concept_file = VOCAB_PATH / "CONCEPT.csv"

    if not concept_file.exists():
        print(f"❌ Archivo no encontrado: {concept_file}")
        return

    try:
        # Leer solo la columna vocabulary_id para ahorrar memoria
        df = pd.read_csv(concept_file, usecols=['vocabulary_id'], sep='\t')

        print("\n=== Conceptos por Vocabulario ===")
        counts = df['vocabulary_id'].value_counts()

        # Mostrar los vocabularios esenciales primero
        essential_vocabs = ["SNOMED", "LOINC", "RxNorm", "CPT4", "ICD10CM", "ATC"]
        for vocab in essential_vocabs:
            if vocab in counts:
                print(f"{vocab}: {counts[vocab]:,}")

        print("\n=== Otros Vocabularios ===")
        for vocab, count in counts.items():
            if vocab not in essential_vocabs:
                print(f"{vocab}: {count:,}")

        print(f"\nTotal de conceptos: {len(df):,}")

    except Exception as e:
        print(f"❌ Error al contar conceptos: {e}")

if __name__ == "__main__":
    print("=== Verificación de Vocabularios OMOP Combinados ===\n")

    check_vocabularies()
    count_concepts()

    print("\n=== Verificación completada ===")
