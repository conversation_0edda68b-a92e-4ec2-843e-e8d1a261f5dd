#!/usr/bin/env python
"""
Script para combinar correctamente los archivos CSV de vocabularios OMOP.
"""
import pandas as pd
import os
from pathlib import Path
import shutil

# Rutas a los directorios
SNOMED_DIR = Path("data/vocabulary/athena/vocabulary_download_v5_{d569a1bc-57f0-47fe-a4c9-0f268dc05726}_1744385761101")
BASIC_DIR = Path("data/vocabulary/athena/vocabulary_download_v5_{a15c407c-21dd-41bd-9a20-0db5831cb2ed}_1744387584392")
COMBINED_DIR = Path("data/vocabulary/combined")
TEMP_DIR = Path("data/vocabulary/temp")

# Archivos a combinar
FILES_TO_COMBINE = [
    "CONCEPT.csv",
    "CONCEPT_RELATIONSHIP.csv",
    "CONCEPT_ANCESTOR.csv",
    "CONCEPT_SYNONYM.csv",
    "VOCABULARY.csv"
]

# Archivos a copiar sin combinar (tomar el más reciente)
FILES_TO_COPY = [
    "CONCEPT_CLASS.csv",
    "DOMAIN.csv",
    "RELATIONSHIP.csv"
]

def combine_csv_files():
    """Combinar archivos CSV de vocabularios."""
    print("=== Combinando archivos CSV de vocabularios ===")
    
    # Crear directorio combinado si no existe
    COMBINED_DIR.mkdir(parents=True, exist_ok=True)
    
    # Combinar archivos
    for file_name in FILES_TO_COMBINE:
        print(f"\nCombinando {file_name}...")
        
        snomed_file = SNOMED_DIR / file_name
        basic_file = BASIC_DIR / file_name
        combined_file = COMBINED_DIR / file_name
        
        if not snomed_file.exists():
            print(f"  ❌ Archivo no encontrado: {snomed_file}")
            continue
            
        if not basic_file.exists():
            print(f"  ❌ Archivo no encontrado: {basic_file}")
            continue
        
        try:
            # Leer archivos
            print(f"  Leyendo {snomed_file}...")
            df_snomed = pd.read_csv(snomed_file, sep='\t', low_memory=False)
            print(f"    {len(df_snomed):,} filas")
            
            print(f"  Leyendo {basic_file}...")
            df_basic = pd.read_csv(basic_file, sep='\t', low_memory=False)
            print(f"    {len(df_basic):,} filas")
            
            # Combinar DataFrames
            print("  Combinando DataFrames...")
            df_combined = pd.concat([df_snomed, df_basic], ignore_index=True)
            
            # Eliminar duplicados si es necesario
            if file_name == "VOCABULARY.csv":
                print("  Eliminando duplicados por vocabulary_id...")
                df_combined = df_combined.drop_duplicates(subset=['vocabulary_id'], keep='first')
            
            # Guardar archivo combinado
            print(f"  Guardando {combined_file}...")
            df_combined.to_csv(combined_file, sep='\t', index=False)
            print(f"    {len(df_combined):,} filas en total")
            
        except Exception as e:
            print(f"  ❌ Error al combinar {file_name}: {e}")
    
    # Copiar archivos sin combinar
    for file_name in FILES_TO_COPY:
        print(f"\nCopiando {file_name}...")
        
        snomed_file = SNOMED_DIR / file_name
        basic_file = BASIC_DIR / file_name
        combined_file = COMBINED_DIR / file_name
        
        # Usar el archivo más reciente
        if basic_file.exists():
            source_file = basic_file
            print(f"  Usando archivo de vocabularios básicos")
        elif snomed_file.exists():
            source_file = snomed_file
            print(f"  Usando archivo de SNOMED")
        else:
            print(f"  ❌ Archivo no encontrado en ningún directorio")
            continue
        
        try:
            # Copiar archivo
            shutil.copy2(source_file, combined_file)
            print(f"  ✅ Archivo copiado correctamente")
        except Exception as e:
            print(f"  ❌ Error al copiar {file_name}: {e}")
    
    print("\n=== Combinación completada ===")

if __name__ == "__main__":
    combine_csv_files()
