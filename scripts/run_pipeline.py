#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to run the FHIR to OMOP CDM transformation pipeline.

This script is a wrapper around the main ETL pipeline that provides
command-line arguments and configuration options.
"""

import argparse
import logging
import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import the main ETL pipeline
from src.fhir_omop.main import run_etl_pipeline

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("etl.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def parse_args():
    """
    Parse command line arguments.
    """
    parser = argparse.ArgumentParser(description='Run FHIR to OMOP CDM transformation pipeline.')
    parser.add_argument('--batch-size', type=int, default=100, help='Number of resources to process per batch')
    parser.add_argument('--config', default='.env', help='Path to configuration file')
    parser.add_argument('--log-level', default='INFO', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'],
                        help='Logging level')
    return parser.parse_args()

def main():
    """
    Main entry point.
    """
    args = parse_args()

    # Set log level
    logging.getLogger().setLevel(getattr(logging, args.log_level))

    # Run the ETL pipeline
    try:
        logger.info(f"Starting ETL pipeline with batch size {args.batch_size}")
        summary = run_etl_pipeline(batch_size=args.batch_size)
        logger.info(f"ETL pipeline completed successfully")
        logger.info(f"Summary: {summary}")
    except Exception as e:
        logger.error(f"Error running ETL pipeline: {e}", exc_info=True)
        sys.exit(1)

if __name__ == '__main__':
    main()
