#!/bin/bash
# Script para configurar el entorno de desarrollo

echo "Configurando entorno de desarrollo..."

# Crear entorno virtual si no existe
if [ ! -d "venv" ]; then
    echo "Creando entorno virtual..."
    python -m venv venv
fi

# Activar entorno virtual
echo "Activando entorno virtual..."
source venv/bin/activate

# Instalar dependencias
echo "Instalando dependencias..."
pip install -e .

# Crear archivo .env si no existe
if [ ! -f ".env" ]; then
    echo "Creando archivo .env..."
    cp .env.example .env
    echo "Por favor, edita el archivo .env con tus configuraciones."
fi

echo "Entorno configurado correctamente."
