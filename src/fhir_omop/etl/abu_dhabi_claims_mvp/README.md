# Abu Dhabi Claims MVP - OMOP Implementation

**Project**: FHIR-OMOP  
**Issue Type**: Task  
**Summary**: Spike – Deploy minimal OMOP Postgres + map first Abu Dhabi claims CSV (MVP)

## 1. Context & Rationale

This MVP implementation lives within the existing **FHIR-OMOP** repository structure under `src/fhir_omop/etl/abu_dhabi_claims_mvp/`. This spike represents a **first, pragmatic pass** to:

- Explore the Abu Dhabi claims CSV (`data/real_test_datasets/claim_anonymized.csv`)
- Map claims data to OMOP CDM tables
- Surface real blockers and implementation challenges
- Create a foundation for the broader FHIR-to-OMOP project

**Learning Goal**: Incremental complexity growth with deep understanding of OMOP fundamentals.

## 2. Scope & Objectives

1. **Deploy vanilla OMOP v5.4 PostgreSQL instance** using Docker
2. **Perform comprehensive EDA** on `claim_anonymized.csv` (~5,000 rows)
3. **Implement MVP ETL** that inserts data into core OMOP tables:
   - `PERSON`
   - `VISIT_OCCURRENCE` 
   - `PROCEDURE_OCCURRENCE`
   - `DRUG_EXPOSURE`
   - `MEASUREMENT`
4. **Document every step** for team reproducibility and learning

## 3. Architecture Alignment

This MVP aligns with the existing project architecture:

```
src/fhir_omop/
├── etl/
│   ├── extractor.py              # General extraction (to be implemented)
│   ├── transformer.py            # General transformation (to be implemented)
│   ├── loader.py                 # General loading (to be implemented)
│   └── abu_dhabi_claims_mvp/     # This MVP
├── mappers/                      # Existing FHIR mappers (reusable patterns)
├── utils/                        # Existing utilities (db_utils.py, etc.)
└── config.py                     # Existing configuration (extendable)
```

## 4. Deliverables

| Deliverable | Location | Notes |
|-------------|----------|-------|
| `docker-compose.yml` + `init_omop.sh` | `env/` | OMOP v5.4 PostgreSQL deployment |
| `profile_report.html` | `eda/` | Auto-generated with pandas-profiling |
| Mapping files | `mappings/` | CPT, Drug codes, `source_to_concept_map.csv` |
| `etl_notebook.ipynb` | `.` | ETL implementation with SQLAlchemy |
| `qc_analysis.sql` + results | `qc/` | Quality control queries and analysis |
| Learning documentation | `learning_notes/` | Pedagogical notes and insights |
| `README.md` | `.` | This comprehensive setup guide |

## 5. Implementation Phases

### Phase 1: Infrastructure Setup (6-8 hours)
- **Database Deployment**: Docker-compose with OMOP v5.4 PostgreSQL
- **Schema Creation**: Official OHDSI DDL scripts
- **Basic Vocabulary**: Load minimal vocabularies for concept mapping
- **Connection Testing**: Verify database connectivity and structure

### Phase 2: Data Analysis & Understanding (8-10 hours)
- **Exploratory Data Analysis**: Comprehensive profiling of claims CSV
- **Medical Code Research**: Understand CPT codes, drug codes, clinical context
- **Mapping Strategy**: Design approach for claims → OMOP transformation
- **Quality Assessment**: Identify data quality issues and limitations

### Phase 3: ETL Implementation (10-12 hours)
- **Core Models**: Implement SQLAlchemy models for OMOP tables
- **Data Access Layer**: Create DAOs for database operations
- **Transformation Logic**: Implement claims-to-OMOP mappers
- **ETL Pipeline**: End-to-end data transformation and loading

### Phase 4: Quality Control & Documentation (4-6 hours)
- **Data Validation**: Implement quality checks and metrics
- **Results Analysis**: Interpret loaded data and identify issues
- **Documentation**: Complete setup guide and lessons learned
- **Knowledge Transfer**: Prepare insights for main project

## 6. External References

- **Shafafiya Dictionary** (CPT, HCPCS, Drug list): [https://www.doh.gov.ae/en/Shafafiya/dictionary](https://www.doh.gov.ae/en/Shafafiya/dictionary)
- **OHDSI Athena** (standard vocabularies): [https://athena.ohdsi.org](https://athena.ohdsi.org)
- **The Book of OHDSI**: [https://ohdsi.github.io/TheBookOfOhdsi/](https://ohdsi.github.io/TheBookOfOhdsi/)
- **HL7 Vulcan FHIR-to-OMOP Guide**: [https://build.fhir.org/ig/HL7/fhir-omop-ig/](https://build.fhir.org/ig/HL7/fhir-omop-ig/)

## 7. Acceptance Criteria

- [ ] PostgreSQL container running (`localhost:5433`) with OMOP schema loaded
- [ ] Raw CSV profiled with key columns documented
- [ ] ≥80% of CPT codes mapped to valid `procedure_concept_id`
- [ ] All drug records inserted into `drug_exposure` (mapped to RxNorm or `concept_id = 0`)
- [ ] QC queries return row counts for each populated OMOP table
- [ ] Complete setup reproducible in ≤15 minutes on clean machine
- [ ] Comprehensive learning documentation for knowledge transfer

## 8. Technical Configuration

### Database Setup
- **Host**: localhost
- **Port**: 5433 (to avoid conflict with FHIR server on 5432)
- **Database**: omop_cdm_abu_dhabi
- **Schema**: public
- **User**: omop_user

### Environment Variables
```bash
# Abu Dhabi OMOP Database
OMOP_ABU_DHABI_DB_HOST=localhost
OMOP_ABU_DHABI_DB_PORT=5433
OMOP_ABU_DHABI_DB_NAME=omop_cdm_abu_dhabi
OMOP_ABU_DHABI_DB_USER=omop_user
OMOP_ABU_DHABI_DB_PASSWORD=secure_password
```

## 9. Success Metrics

### Technical Metrics
- Database deployment time: <5 minutes
- ETL processing time: <30 minutes for full dataset
- Data quality: >95% successful record processing
- Concept mapping: >80% CPT codes successfully mapped

### Learning Metrics
- Complete understanding of OMOP CDM structure
- Ability to explain vocabulary mapping process
- Knowledge of data quality patterns in claims data
- Documented insights for main project development

## 10. Risk Mitigation

### Identified Risks
1. **Vocabulary Complexity**: OMOP vocabularies are extensive
   - *Mitigation*: Start with minimal vocabulary subset
2. **Data Quality Issues**: Claims data may have inconsistencies
   - *Mitigation*: Implement robust validation and error handling
3. **Learning Curve**: OMOP concepts are complex
   - *Mitigation*: Incremental learning approach with documentation

### Contingency Plans
- If full vocabulary loading fails: Use basic concept mappings
- If complex mappings fail: Document unmapped codes for future work
- If performance issues: Implement batch processing

## 11. Next Steps

After MVP completion:
1. **Integration**: Incorporate learnings into main FHIR-to-OMOP pipeline
2. **Scaling**: Apply patterns to larger datasets
3. **Enhancement**: Implement advanced vocabulary features
4. **Documentation**: Update main project documentation with insights

## 12. Getting Started

1. Review the [Learning Guide](LEARNING_GUIDE.md) for pedagogical approach
2. Set up the development environment following `env/` instructions
3. Begin with Phase 1: Infrastructure Setup
4. Document learning progress in `learning_notes/`

---

**Estimated Total Effort**: 28-36 hours (3.5-4.5 work days)  
**Approach**: Incremental learning with comprehensive documentation  
**Success Criteria**: Technical deliverables + deep OMOP understanding
