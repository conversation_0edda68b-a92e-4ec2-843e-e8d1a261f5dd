{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Abu Dhabi Claims OMOP MVP - Interactive Learning Notebook\n", "\n", "**Objective**: Master OMOP CDM fundamentals through incremental, hands-on implementation\n", "\n", "This notebook provides an interactive learning companion for the Abu Dhabi Claims OMOP MVP implementation. Follow each phase systematically, executing code cells and documenting your learning journey.\n", "\n", "## Learning Philosophy\n", "\n", "- **Theory Before Practice**: Understand the WHY before the HOW\n", "- **Incremental Complexity**: Each phase builds on previous knowledge\n", "- **Active Documentation**: Write to learn, learn to write\n", "- **Clinical Context**: Connect technical decisions to healthcare needs\n", "- **Reflective Practice**: Regular assessment of understanding\n", "\n", "## Notebook Structure\n", "\n", "- **Phase 0**: Theoretical Foundation (4-6 hours)\n", "- **Phase 1**: Infrastructure Deep Dive (6-8 hours)\n", "- **Phase 2**: Data Analysis Mastery (8-10 hours)\n", "- **Phase 3**: Implementation Excellence (10-12 hours)\n", "- **Phase 4**: Validation & Mastery (4-6 hours)\n", "\n", "---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 🎯 Phase 0: Theoretical Foundation (4-6 hours)\n", "*\"Understanding the Healthcare Data Challenge\"*\n", "\n", "## Learning Objectives\n", "- Comprehend the interoperability problem in healthcare\n", "- Understand OMOP's role in standardizing clinical data\n", "- Recognize the value of common data models\n", "- Identify key stakeholders and use cases"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Setup and Initial Exploration\n", "\n", "Let's start by setting up our environment and taking our first look at the data."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import essential libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from pathlib import Path\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set up plotting\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"✅ Environment setup complete!\")\n", "print(\"📚 Ready to begin OMOP learning journey\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load the Abu Dhabi claims dataset\n", "data_path = \"../../../../data/real_test_datasets/claim_anonymized.csv\"\n", "\n", "try:\n", "    claims_df = pd.read_csv(data_path)\n", "    print(f\"✅ Successfully loaded claims data\")\n", "    print(f\"📊 Dataset shape: {claims_df.shape}\")\n", "    print(f\"📋 Columns: {len(claims_df.columns)}\")\n", "except FileNotFoundError:\n", "    print(\"❌ Claims data file not found. Please check the path.\")\n", "    print(f\"🔍 Looking for: {data_path}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. First Look at Healthcare Claims Data\n", "\n", "Before diving into OMOP theory, let's understand what we're working with."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Basic dataset overview\n", "print(\"🏥 ABU DHABI CLAIMS DATASET OVERVIEW\")\n", "print(\"=\" * 50)\n", "print(f\"Total records: {len(claims_df):,}\")\n", "print(f\"Total columns: {len(claims_df.columns)}\")\n", "print(f\"Memory usage: {claims_df.memory_usage(deep=True).sum() / 1024**2:.2f} MB\")\n", "print(\"\\n📋 Column Names:\")\n", "for i, col in enumerate(claims_df.columns, 1):\n", "    print(f\"{i:2d}. {col}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Sample data examination\n", "print(\"🔍 SAMPLE RECORDS\")\n", "print(\"=\" * 50)\n", "display(claims_df.head(3))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 🤔 Reflection Questions - Initial Data Exploration\n", "\n", "Take a moment to examine the data and answer these questions:"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Question 1**: What types of healthcare information do you see in this dataset?\n", "\n", "*Your answer:*\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Question 2**: What challenges might exist in sharing this data between different healthcare systems?\n", "\n", "*Your answer:*\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Question 3**: What medical codes do you notice? (Look for CPT codes, drug codes, etc.)\n", "\n", "*Your answer:*\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Understanding the Healthcare Interoperability Challenge\n", "\n", "Now let's explore why OMOP exists by examining the data diversity."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Explore data diversity - the interoperability challenge\n", "print(\"🌍 DATA DIVERSITY ANALYSIS\")\n", "print(\"=\" * 50)\n", "\n", "# Unique values in key fields\n", "key_fields = ['provider_id', 'institution_name', 'case_type', 'code_activity', 'type_activity']\n", "\n", "for field in key_fields:\n", "    if field in claims_df.columns:\n", "        unique_count = claims_df[field].nunique()\n", "        print(f\"{field}: {unique_count} unique values\")\n", "        \n", "        # Show sample values\n", "        sample_values = claims_df[field].value_counts().head(3)\n", "        print(f\"  Top values: {list(sample_values.index)}\")\n", "        print()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Introduction to OMOP CDM\n", "\n", "Let's understand what OMOP is and why it matters."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 📚 OMOP CDM Key Concepts\n", "\n", "**OMOP (Observational Medical Outcomes Partnership) Common Data Model** is a standardized data structure that enables:\n", "\n", "1. **Interoperability**: Different healthcare systems can share data\n", "2. **Standardization**: Common vocabulary and structure\n", "3. **Research**: Large-scale observational studies\n", "4. **Analytics**: Consistent analysis across institutions\n", "\n", "### 🏗️ Core OMOP Domains\n", "\n", "- **Person**: Patient demographics\n", "- **Visit**: Healthcare encounters\n", "- **Condition**: Diagnoses and medical conditions\n", "- **Procedure**: Medical procedures and interventions\n", "- **Drug**: Medications and treatments\n", "- **Measurement**: Lab results and vital signs\n", "- **Observation**: Other clinical observations\n", "\n", "### 🔗 Key Resources\n", "- [OHDSI Collaborative](https://www.ohdsi.org/)\n", "- [OMOP CDM Documentation](https://ohdsi.github.io/CommonDataModel/)\n", "- [Book of OHDSI](https://ohdsi.github.io/TheBookOfOhdsi/)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Let's map our claims data to OMOP domains conceptually\n", "print(\"🗺️ CONCEPTUAL MAPPING: CLAIMS → OMOP DOMAINS\")\n", "print(\"=\" * 60)\n", "\n", "# Analyze what OMOP domains our data might map to\n", "domain_mapping = {\n", "    'Person Domain': ['aio_patient_id', 'unique_id'],\n", "    'Visit Domain': ['case', 'encounter_start_date', 'encounter_end_date', 'case_type'],\n", "    'Procedure Domain': ['code_activity', 'activity_desc', 'type_activity'],\n", "    'Drug Domain': [],  # We'll identify these\n", "    'Measurement Domain': [],  # We'll identify these\n", "    'Provider Domain': ['provider_id', 'clinician', 'clinician_name'],\n", "    'Payer Domain': ['payer_id', 'payer_id_desc', 'insurance_plan_id']\n", "}\n", "\n", "for domain, fields in domain_mapping.items():\n", "    print(f\"\\n{domain}:\")\n", "    available_fields = [f for f in fields if f in claims_df.columns]\n", "    if available_fields:\n", "        for field in available_fields:\n", "            print(f\"  ✅ {field}\")\n", "    else:\n", "        print(f\"  ❓ Fields to be identified\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 🤔 Reflection Questions - OMOP Understanding\n", "\n", "Now that you've learned about OMOP, reflect on these questions:"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Question 4**: Why can't hospitals just share data directly without a common data model?\n", "\n", "*Your answer:*\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Question 5**: What makes healthcare data different from other domains (e.g., e-commerce, finance)?\n", "\n", "*Your answer:*\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Question 6**: How does OMOP enable research across institutions?\n", "\n", "*Your answer:*\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. <PERSON><PERSON><PERSON> Data Deep Dive\n", "\n", "Let's understand the clinical context of our data."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyze activity types and codes\n", "print(\"🏥 CLINICAL ACTIVITY ANALYSIS\")\n", "print(\"=\" * 50)\n", "\n", "# Activity types\n", "if 'type_activity' in claims_df.columns:\n", "    activity_types = claims_df['type_activity'].value_counts()\n", "    print(\"Activity Types:\")\n", "    for activity_type, count in activity_types.head(10).items():\n", "        print(f\"  {activity_type}: {count:,} records\")\n", "\n", "print(\"\\n\" + \"=\" * 50)\n", "\n", "# Sample activity codes and descriptions\n", "if 'code_activity' in claims_df.columns and 'activity_desc' in claims_df.columns:\n", "    print(\"\\nSample Medical Codes:\")\n", "    sample_codes = claims_df[['code_activity', 'activity_desc']].drop_duplicates().head(10)\n", "    for _, row in sample_codes.iterrows():\n", "        print(f\"  {row['code_activity']}: {row['activity_desc'][:60]}...\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Identify potential drug codes\n", "print(\"💊 DRUG CODE IDENTIFICATION\")\n", "print(\"=\" * 50)\n", "\n", "# Look for drug-like patterns in activity codes\n", "if 'code_activity' in claims_df.columns:\n", "    # Drug codes often contain letters and numbers in specific patterns\n", "    drug_pattern_mask = claims_df['code_activity'].str.contains(r'[A-Z]\\d+-\\d+', na=False)\n", "    potential_drugs = claims_df[drug_pattern_mask]\n", "    \n", "    if len(potential_drugs) > 0:\n", "        print(f\"Found {len(potential_drugs)} potential drug records\")\n", "        print(\"\\nSample drug codes:\")\n", "        drug_samples = potential_drugs[['code_activity', 'activity_desc']].drop_duplicates().head(5)\n", "        for _, row in drug_samples.iterrows():\n", "            print(f\"  {row['code_activity']}: {row['activity_desc']}\")\n", "    else:\n", "        print(\"No obvious drug code patterns found\")\n", "        print(\"Will need to investigate activity types further\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Phase 0 Learning Summary\n", "\n", "Document your key learnings from this foundational phase."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 📝 My Phase 0 Learning Notes\n", "\n", "**Key Concepts Learned:**\n", "1. \n", "2. \n", "3. \n", "\n", "**Questions That Arose:**\n", "1. \n", "2. \n", "3. \n", "\n", "**Initial Mapping Hypotheses:**\n", "1. \n", "2. \n", "3. \n", "\n", "**Areas Needing Deeper Investigation:**\n", "1. \n", "2. \n", "3. "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Save your Phase 0 insights\n", "phase0_insights = {\n", "    'dataset_shape': claims_df.shape,\n", "    'unique_patients': claims_df['aio_patient_id'].nunique() if 'aio_patient_id' in claims_df.columns else 'Unknown',\n", "    'date_range': {\n", "        'start': claims_df['encounter_start_date'].min() if 'encounter_start_date' in claims_df.columns else 'Unknown',\n", "        'end': claims_df['encounter_start_date'].max() if 'encounter_start_date' in claims_df.columns else 'Unknown'\n", "    },\n", "    'activity_types': claims_df['type_activity'].value_counts().to_dict() if 'type_activity' in claims_df.columns else {},\n", "    'completion_timestamp': pd.Timestamp.now().isoformat()\n", "}\n", "\n", "print(\"✅ Phase 0 Complete!\")\n", "print(f\"📊 Analyzed {phase0_insights['dataset_shape'][0]:,} records\")\n", "print(f\"👥 Covering {phase0_insights['unique_patients']} unique patients\")\n", "print(\"🎯 Ready for Phase 1: Infrastructure Deep Dive\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### ✅ Phase 0 Completion Checklist\n", "\n", "Before moving to Phase 1, ensure you can answer YES to these questions:\n", "\n", "- [ ] Can I explain OMOP's purpose to a colleague?\n", "- [ ] Do I understand the claims-to-OMOP transformation challenge?\n", "- [ ] Have I documented initial questions and hypotheses?\n", "- [ ] Am I ready to dive into technical implementation?\n", "\n", "---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 🏗️ Phase 1: Infrastructure Deep Dive (6-8 hours)\n", "*\"Building the OMOP Laboratory\"*\n", "\n", "## Learning Objectives\n", "- Master OMOP database structure and relationships\n", "- Understand PostgreSQL optimization for healthcare data\n", "- Comprehend vocabulary architecture and dependencies\n", "- Implement reproducible infrastructure"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Database Connection Setup\n", "\n", "First, let's establish our connection to the OMOP database."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Database connection setup\n", "import sqlalchemy as sa\n", "import psycopg2\n", "from sqlalchemy import create_engine, text\n", "import os\n", "\n", "# Database configuration for Abu Dhabi OMOP\n", "DB_CONFIG = {\n", "    'host': 'localhost',\n", "    'port': '5433',  # Different from FHIR server to avoid conflicts\n", "    'database': 'omop_cdm_abu_dhabi',\n", "    'username': 'omop_user',\n", "    'password': 'secure_password',\n", "    'schema': 'public'\n", "}\n", "\n", "# Create connection string\n", "connection_string = f\"postgresql://{DB_CONFIG['username']}:{DB_CONFIG['password']}@{DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}\"\n", "\n", "print(\"🔧 Database Configuration:\")\n", "print(f\"  Host: {DB_CONFIG['host']}\")\n", "print(f\"  Port: {DB_CONFIG['port']}\")\n", "print(f\"  Database: {DB_CONFIG['database']}\")\n", "print(f\"  Schema: {DB_CONFIG['schema']}\")\n", "print(\"\\n⚠️  Note: Make sure your OMOP database is running before executing the next cell!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test database connection\n", "try:\n", "    engine = create_engine(connection_string)\n", "    \n", "    # Test connection\n", "    with engine.connect() as conn:\n", "        result = conn.execute(text(\"SELECT version();\"))\n", "        version = result.fetchone()[0]\n", "        \n", "    print(\"✅ Database connection successful!\")\n", "    print(f\"📊 PostgreSQL version: {version.split(',')[0]}\")\n", "    \n", "except Exception as e:\n", "    print(\"❌ Database connection failed!\")\n", "    print(f\"Error: {str(e)}\")\n", "    print(\"\\n🔧 Troubleshooting steps:\")\n", "    print(\"1. Ensure PostgreSQL container is running\")\n", "    print(\"2. Check connection parameters\")\n", "    print(\"3. Verify database and user exist\")\n", "    engine = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. OMOP Schema Exploration\n", "\n", "Let's systematically explore the OMOP database structure."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Explore OMOP table structure\n", "if engine:\n", "    print(\"🏗️ OMOP CDM TABLE STRUCTURE\")\n", "    print(\"=\" * 60)\n", "    \n", "    # Get all tables\n", "    tables_query = \"\"\"\n", "    SELECT table_name \n", "    FROM information_schema.tables \n", "    WHERE table_schema = 'public' \n", "    ORDER BY table_name;\n", "    \"\"\"\n", "    \n", "    with engine.connect() as conn:\n", "        tables_result = conn.execute(text(tables_query))\n", "        tables = [row[0] for row in tables_result.fetchall()]\n", "    \n", "    # Categorize tables by domain\n", "    table_categories = {\n", "        'Clinical Data': ['person', 'visit_occurrence', 'condition_occurrence', \n", "                         'procedure_occurrence', 'drug_exposure', 'measurement', \n", "                         'observation', 'death'],\n", "        'Vocabulary': ['concept', 'vocabulary', 'domain', 'concept_class', \n", "                      'concept_relationship', 'relationship', 'concept_synonym', \n", "                      'concept_ancestor', 'source_to_concept_map', 'drug_strength'],\n", "        'Healthcare System': ['provider', 'care_site', 'location'],\n", "        'Health Economics': ['payer_plan_period', 'cost'],\n", "        'Metadata': ['cdm_source', 'metadata']\n", "    }\n", "    \n", "    for category, expected_tables in table_categories.items():\n", "        print(f\"\\n{category}:\")\n", "        found_tables = [t for t in expected_tables if t in tables]\n", "        missing_tables = [t for t in expected_tables if t not in tables]\n", "        \n", "        for table in found_tables:\n", "            print(f\"  ✅ {table}\")\n", "        for table in missing_tables:\n", "            print(f\"  ❌ {table} (missing)\")\n", "    \n", "    print(f\"\\n📊 Total tables found: {len(tables)}\")\nelse:\n", "    print(\"❌ Cannot explore schema - no database connection\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 🤔 Reflection Questions - Database Structure\n", "\n", "Examine the table structure and think about these questions:"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Question 7**: Why does OMOP separate clinical facts into different tables instead of one big table?\n", "\n", "*Your answer:*\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Question 8**: What is the purpose of having separate vocabulary tables?\n", "\n", "*Your answer:*\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Examine key table structures\n", "if engine:\n", "    key_tables = ['person', 'visit_occurrence', 'concept']\n", "    \n", "    for table_name in key_tables:\n", "        print(f\"\\n🔍 {table_name.upper()} TABLE STRUCTURE\")\n", "        print(\"=\" * 50)\n", "        \n", "        # Get column information\n", "        columns_query = f\"\"\"\n", "        SELECT column_name, data_type, is_nullable, column_default\n", "        FROM information_schema.columns \n", "        WHERE table_name = '{table_name}' \n", "        AND table_schema = 'public'\n", "        ORDER BY ordinal_position;\n", "        \"\"\"\n", "        \n", "        try:\n", "            with engine.connect() as conn:\n", "                columns_df = pd.read_sql(columns_query, conn)\n", "            \n", "            if not columns_df.empty:\n", "                for _, row in columns_df.iterrows():\n", "                    nullable = \"NULL\" if row['is_nullable'] == 'YES' else \"NOT NULL\"\n", "                    default = f\" DEFAULT {row['column_default']}\" if row['column_default'] else \"\"\n", "                    print(f\"  {row['column_name']:<25} {row['data_type']:<15} {nullable}{default}\")\n", "            else:\n", "                print(f\"  ❌ Table {table_name} not found or empty\")\n", "                \n", "        except Exception as e:\n", "            print(f\"  ❌ Error examining {table_name}: {str(e)}\")\nelse:\n", "    print(\"❌ Cannot examine tables - no database connection\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Vocabulary Deep Dive\n", "\n", "Understanding OMOP vocabularies is crucial for concept mapping."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Explore vocabulary structure\n", "if engine:\n", "    print(\"📚 OMOP VOCABULARY EXPLORATION\")\n", "    print(\"=\" * 60)\n", "    \n", "    # Check if vocabularies are loaded\n", "    vocab_query = \"\"\"\n", "    SELECT vocabulary_id, vocabulary_name, vocabulary_version, vocabulary_concept_id\n", "    FROM vocabulary \n", "    ORDER BY vocabulary_id;\n", "    \"\"\"\n", "    \n", "    try:\n", "        with engine.connect() as conn:\n", "            vocab_df = pd.read_sql(vocab_query, conn)\n", "        \n", "        if not vocab_df.empty:\n", "            print(f\"✅ Found {len(vocab_df)} vocabularies loaded\")\n", "            print(\"\\nAvailable vocabularies:\")\n", "            for _, row in vocab_df.head(10).iterrows():\n", "                print(f\"  {row['vocabulary_id']:<15} {row['vocabulary_name']}\")\n", "            \n", "            if len(vocab_df) > 10:\n", "                print(f\"  ... and {len(vocab_df) - 10} more\")\n", "        else:\n", "            print(\"❌ No vocabularies found - need to load basic vocabularies\")\n", "            \n", "    except Exception as e:\n", "        print(f\"❌ Error querying vocabularies: {str(e)}\")\n", "        print(\"This might indicate vocabularies are not yet loaded\")\nelse:\n", "    print(\"❌ Cannot explore vocabularies - no database connection\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Explore concept structure (if vocabularies are loaded)\n", "if engine:\n", "    print(\"🔍 CONCEPT STRUCTURE EXPLORATION\")\n", "    print(\"=\" * 60)\n", "    \n", "    concept_count_query = \"SELECT COUNT(*) as concept_count FROM concept;\"\n", "    \n", "    try:\n", "        with engine.connect() as conn:\n", "            result = conn.execute(text(concept_count_query))\n", "            concept_count = result.fetchone()[0]\n", "        \n", "        print(f\"📊 Total concepts in database: {concept_count:,}\")\n", "        \n", "        if concept_count > 0:\n", "            # Sample concepts by domain\n", "            sample_concepts_query = \"\"\"\n", "            SELECT concept_id, concept_name, domain_id, vocabulary_id, concept_class_id\n", "            FROM concept \n", "            WHERE standard_concept = 'S'\n", "            ORDER BY concept_id \n", "            LIMIT 10;\n", "            \"\"\"\n", "            \n", "            with engine.connect() as conn:\n", "                sample_df = pd.read_sql(sample_concepts_query, conn)\n", "            \n", "            print(\"\\nSample standard concepts:\")\n", "            for _, row in sample_df.iterrows():\n", "                print(f\"  {row['concept_id']:<10} {row['domain_id']:<12} {row['concept_name'][:40]}...\")\n", "        else:\n", "            print(\"⚠️  No concepts loaded - will need to load basic vocabularies\")\n", "            \n", "    except Exception as e:\n", "        print(f\"❌ Error exploring concepts: {str(e)}\")\nelse:\n", "    print(\"❌ Cannot explore concepts - no database connection\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Phase 1 Learning Summary\n", "\n", "Document your infrastructure learning."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 📝 My Phase 1 Learning Notes\n", "\n", "**Database Architecture Insights:**\n", "1. \n", "2. \n", "3. \n", "\n", "**Vocabulary System Understanding:**\n", "1. \n", "2. \n", "3. \n", "\n", "**Infrastructure Decisions Made:**\n", "1. \n", "2. \n", "3. \n", "\n", "**Questions for Further Investigation:**\n", "1. \n", "2. \n", "3. "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Save Phase 1 infrastructure insights\n", "if engine:\n", "    # Test basic functionality\n", "    with engine.connect() as conn:\n", "        # Count tables\n", "        tables_count = conn.execute(text(\"\"\"\n", "            SELECT COUNT(*) FROM information_schema.tables \n", "            WHERE table_schema = 'public'\n", "        \"\"\")).fetchone()[0]\n", "        \n", "        # Count vocabularies\n", "        try:\n", "            vocab_count = conn.execute(text(\"SELECT COUNT(*) FROM vocabulary\")).fetchone()[0]\n", "        except:\n", "            vocab_count = 0\n", "        \n", "        # Count concepts\n", "        try:\n", "            concept_count = conn.execute(text(\"SELECT COUNT(*) FROM concept\")).fetchone()[0]\n", "        except:\n", "            concept_count = 0\n", "    \n", "    phase1_insights = {\n", "        'database_connection': 'successful',\n", "        'tables_count': tables_count,\n", "        'vocabularies_loaded': vocab_count,\n", "        'concepts_loaded': concept_count,\n", "        'infrastructure_ready': tables_count > 20,  # Basic OMOP has ~40 tables\n", "        'completion_timestamp': pd.Timestamp.now().isoformat()\n", "    }\n", "    \n", "    print(\"✅ Phase 1 Complete!\")\n", "    print(f\"🏗️  Database tables: {phase1_insights['tables_count']}\")\n", "    print(f\"📚 Vocabularies loaded: {phase1_insights['vocabularies_loaded']}\")\n", "    print(f\"🔍 Concepts available: {phase1_insights['concepts_loaded']:,}\")\n", "    print(\"🎯 Ready for Phase 2: Data Analysis Mastery\")\n", "    \nelse:\n", "    print(\"❌ Phase 1 incomplete - database connection issues\")\n", "    print(\"🔧 Please resolve infrastructure setup before proceeding\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### ✅ Phase 1 Completion Checklist\n", "\n", "Before moving to Phase 2, ensure you can answer YES to these questions:\n", "\n", "- [ ] Is the OMOP database running and accessible?\n", "- [ ] Can I navigate the vocabulary structure confidently?\n", "- [ ] Do I understand table relationships and constraints?\n", "- [ ] Have I documented infrastructure decisions and rationale?\n", "\n", "---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 🔍 Phase 2: Data Analysis Mastery (8-10 hours)\n", "*\"Becoming a Healthcare Data Detective\"*\n", "\n", "## Learning Objectives\n", "- Master exploratory data analysis for clinical data\n", "- Understand medical coding systems (CPT, NDC, etc.)\n", "- Identify data quality patterns in healthcare\n", "- Design mapping strategies based on data characteristics"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Comprehensive Clinical Data Exploration\n", "\n", "Let's dive deep into the clinical context of our claims data."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Advanced data profiling setup\n", "from ydata_profiling import ProfileReport\n", "import plotly.express as px\n", "import plotly.graph_objects as go\n", "from plotly.subplots import make_subplots\n", "\n", "print(\"🔍 COMPREHENSIVE CLINICAL DATA ANALYSIS\")\n", "print(\"=\" * 60)\n", "\n", "# Reload data if needed\n", "if 'claims_df' not in locals() or claims_df is None:\n", "    claims_df = pd.read_csv(\"../../../../data/real_test_datasets/claim_anonymized.csv\")\n", "\n", "print(f\"📊 Dataset loaded: {claims_df.shape[0]:,} records, {claims_df.shape[1]} columns\")\n", "print(f\"📅 Date range: {claims_df['encounter_start_date'].min()} to {claims_df['encounter_start_date'].max()}\")\n", "print(f\"👥 Unique patients: {claims_df['aio_patient_id'].nunique():,}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Clinical encounter analysis\n", "print(\"🏥 CLINICAL ENCOUNTER PATTERNS\")\n", "print(\"=\" * 50)\n", "\n", "# Encounter types analysis\n", "encounter_types = claims_df['case_type'].value_counts()\n", "print(\"\\nEncounter Types:\")\n", "for enc_type, count in encounter_types.items():\n", "    percentage = (count / len(claims_df)) * 100\n", "    print(f\"  {enc_type}: {count:,} ({percentage:.1f}%)\")\n", "\n", "# Activity types analysis\n", "if 'type_activity' in claims_df.columns:\n", "    activity_types = claims_df['type_activity'].value_counts()\n", "    print(\"\\nActivity Types:\")\n", "    for act_type, count in activity_types.head(10).items():\n", "        percentage = (count / len(claims_df)) * 100\n", "        print(f\"  {act_type}: {count:,} ({percentage:.1f}%)\")\n", "\n", "# Patient encounter frequency\n", "patient_encounters = claims_df.groupby('aio_patient_id').size()\n", "print(f\"\\n👥 Patient Encounter Statistics:\")\n", "print(f\"  Average encounters per patient: {patient_encounters.mean():.1f}\")\n", "print(f\"  Median encounters per patient: {patient_encounters.median():.1f}\")\n", "print(f\"  Max encounters per patient: {patient_encounters.max()}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Medical codes deep dive\n", "print(\"💊 MEDICAL CODES ANALYSIS\")\n", "print(\"=\" * 50)\n", "\n", "# Analyze activity codes patterns\n", "if 'code_activity' in claims_df.columns:\n", "    unique_codes = claims_df['code_activity'].nunique()\n", "    total_activities = len(claims_df)\n", "    \n", "    print(f\"📋 Total unique medical codes: {unique_codes:,}\")\n", "    print(f\"📊 Total activities: {total_activities:,}\")\n", "    print(f\"🔄 Code reuse ratio: {total_activities/unique_codes:.1f}x\")\n", "    \n", "    # Most common codes\n", "    top_codes = claims_df['code_activity'].value_counts().head(10)\n", "    print(\"\\n🔝 Most Common Medical Codes:\")\n", "    for code, count in top_codes.items():\n", "        # Get description\n", "        desc = claims_df[claims_df['code_activity'] == code]['activity_desc'].iloc[0]\n", "        print(f\"  {code}: {count:,} times - {desc[:50]}...\")\n", "    \n", "    # Identify code patterns\n", "    print(\"\\n🔍 Code Pattern Analysis:\")\n", "    \n", "    # CPT-like codes (5 digits)\n", "    cpt_pattern = claims_df['code_activity'].str.match(r'^\\d{5}$', na=False)\n", "    cpt_count = cpt_pattern.sum()\n", "    print(f\"  CPT-like codes (5 digits): {cpt_count:,} ({cpt_count/len(claims_df)*100:.1f}%)\")\n", "    \n", "    # Drug-like codes (letters + numbers)\n", "    drug_pattern = claims_df['code_activity'].str.contains(r'[A-Z]\\d+-\\d+', na=False)\n", "    drug_count = drug_pattern.sum()\n", "    print(f\"  Drug-like codes: {drug_count:,} ({drug_count/len(claims_df)*100:.1f}%)\")\n", "    \n", "    # Dental codes (5 digits starting with 2 or 7)\n", "    dental_pattern = claims_df['code_activity'].str.match(r'^[27]\\d{4}$', na=False)\n", "    dental_count = dental_pattern.sum()\n", "    print(f\"  Dental-like codes: {dental_count:,} ({dental_count/len(claims_df)*100:.1f}%)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 🤔 Reflection Questions - Clinical Data Patterns\n", "\n", "Analyze the patterns you've discovered:"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Question 9**: What types of medical encounters are most common in this dataset?\n", "\n", "*Your answer:*\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Question 10**: What do the code patterns tell you about the types of medical services provided?\n", "\n", "*Your answer:*\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Generate comprehensive data profile\n", "print(\"📊 GENERATING COMPREHENSIVE DATA PROFILE\")\n", "print(\"=\" * 60)\n", "print(\"⏳ This may take a few minutes for large datasets...\")\n", "\n", "# Create a sample for profiling if dataset is large\n", "if len(claims_df) > 10000:\n", "    sample_size = 5000\n", "    profile_df = claims_df.sample(n=sample_size, random_state=42)\n", "    print(f\"📋 Using sample of {sample_size:,} records for profiling\")\nelse:\n", "    profile_df = claims_df\n", "    print(f\"📋 Profiling full dataset of {len(profile_df):,} records\")\n", "\n", "try:\n", "    # Generate profile report\n", "    profile = ProfileReport(\n", "        profile_df, \n", "        title=\"Abu Dhabi Claims Dataset Profile\",\n", "        explorative=True,\n", "        minimal=False\n", "    )\n", "    \n", "    # Save the report\n", "    profile_path = \"eda/profile_report.html\"\n", "    profile.to_file(profile_path)\n", "    \n", "    print(f\"✅ Profile report generated: {profile_path}\")\n", "    print(\"📖 Open the HTML file in your browser for detailed analysis\")\n", "    \nexcept Exception as e:\n", "    print(f\"❌ Error generating profile: {str(e)}\")\n", "    print(\"💡 You can continue with manual analysis below\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Medical Code Research\n", "\n", "Let's research the medical codes to understand their clinical meaning."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Research top CPT codes\n", "print(\"🔬 MEDICAL CODE RESEARCH\")\n", "print(\"=\" * 50)\n", "\n", "# Extract CPT codes for research\n", "cpt_codes = claims_df[claims_df['code_activity'].str.match(r'^\\d{5}$', na=False)]\n", "top_cpt = cpt_codes['code_activity'].value_counts().head(10)\n", "\n", "print(\"🏥 Top CPT Codes for Research:\")\n", "cpt_research_list = []\n", "\n", "for code, count in top_cpt.items():\n", "    desc = cpt_codes[cpt_codes['code_activity'] == code]['activity_desc'].iloc[0]\n", "    cpt_research_list.append({\n", "        'code': code,\n", "        'description': desc,\n", "        'frequency': count,\n", "        'percentage': (count / len(claims_df)) * 100\n", "    })\n", "    print(f\"  {code}: {desc[:60]}... ({count:,} times)\")\n", "\n", "print(\"\\n📚 Research these codes at:\")\n", "print(\"  - CMS CPT Code Lookup: https://www.cms.gov/medicare/coding/MedHCPCSGenInfo\")\n", "print(\"  - Shafafiya Dictionary: https://www.doh.gov.ae/en/Shafafiya/dictionary\")\n", "print(\"  - AAPC CPT Lookup: https://www.aapc.com/codes/\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Research drug codes\n", "print(\"💊 DRUG CODE RESEARCH\")\n", "print(\"=\" * 50)\n", "\n", "# Extract drug codes\n", "drug_codes = claims_df[claims_df['code_activity'].str.contains(r'[A-Z]\\d+-\\d+', na=False)]\n", "\n", "if len(drug_codes) > 0:\n", "    top_drugs = drug_codes['code_activity'].value_counts().head(10)\n", "    \n", "    print(\"💊 Top Drug Codes for Research:\")\n", "    drug_research_list = []\n", "    \n", "    for code, count in top_drugs.items():\n", "        desc = drug_codes[drug_codes['code_activity'] == code]['activity_desc'].iloc[0]\n", "        drug_research_list.append({\n", "            'code': code,\n", "            'description': desc,\n", "            'frequency': count,\n", "            'percentage': (count / len(claims_df)) * 100\n", "        })\n", "        print(f\"  {code}: {desc[:60]}... ({count:,} times)\")\n", "    \n", "    print(\"\\n🔍 Research these drugs at:\")\n", "    print(\"  - RxN<PERSON> Browser: https://mor.nlm.nih.gov/RxNav/\")\n", "    print(\"  - Drugs.com: https://www.drugs.com/\")\n", "    print(\"  - UAE Drug Index: Local pharmaceutical references\")\nelse:\n", "    print(\"❌ No obvious drug codes found with current pattern\")\n", "    print(\"🔍 May need to investigate other activity types\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 📝 Medical Code Research Notes\n", "\n", "Document your research findings for the top codes:"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**CPT Code Research:**\n", "\n", "| Code | Clinical Meaning | OMOP Mapping Strategy |\n", "|------|------------------|----------------------|\n", "| | | |\n", "| | | |\n", "| | | |\n", "\n", "**Drug Code Research:**\n", "\n", "| Code | Drug Name/Type | OMOP Mapping Strategy |\n", "|------|----------------|----------------------|\n", "| | | |\n", "| | | |\n", "| | | |"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. OMOP Mapping Strategy Development\n", "\n", "Based on our data analysis, let's design our transformation approach."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Design mapping strategy\n", "print(\"🗺️ OMOP MAPPING STRATEGY DESIGN\")\n", "print(\"=\" * 60)\n", "\n", "# Analyze data for OMOP domain mapping\n", "mapping_strategy = {\n", "    'Person': {\n", "        'source_fields': ['aio_patient_id', 'unique_id'],\n", "        'challenges': ['No demographic data', 'Need to generate person records'],\n", "        'approach': 'Create person records from unique patient IDs'\n", "    },\n", "    'Visit_Occurrence': {\n", "        'source_fields': ['case', 'encounter_start_date', 'encounter_end_date', 'case_type'],\n", "        'challenges': ['Multiple activities per visit', 'Visit type mapping'],\n", "        'approach': 'Group activities by case ID and dates'\n", "    },\n", "    'Procedure_Occurrence': {\n", "        'source_fields': ['code_activity', 'activity_desc', 'type_activity'],\n", "        'challenges': ['CPT code mapping', 'Mixed activity types'],\n", "        'approach': 'Map CPT codes to OMOP procedure concepts'\n", "    },\n", "    'Drug_Exposure': {\n", "        'source_fields': ['code_activity', 'activity_desc'],\n", "        'challenges': ['Local drug codes', 'Limited drug information'],\n", "        'approach': 'Map drug codes to RxNorm or use concept_id=0'\n", "    },\n", "    'Provider': {\n", "        'source_fields': ['provider_id', 'clinician', 'clinician_name'],\n", "        'challenges': ['Provider specialty unknown'],\n", "        'approach': 'Create provider records from unique identifiers'\n", "    }\n", "}\n", "\n", "for domain, details in mapping_strategy.items():\n", "    print(f\"\\n🏥 {domain}:\")\n", "    print(f\"  Source fields: {', '.join(details['source_fields'])}\")\n", "    print(f\"  Approach: {details['approach']}\")\n", "    print(f\"  Challenges: {'; '.join(details['challenges'])}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Data quality assessment for mapping\n", "print(\"\\n📊 DATA QUALITY ASSESSMENT FOR MAPPING\")\n", "print(\"=\" * 60)\n", "\n", "quality_metrics = {}\n", "\n", "# Check completeness of key fields\n", "key_fields = ['aio_patient_id', 'case', 'code_activity', 'encounter_start_date']\n", "\n", "for field in key_fields:\n", "    if field in claims_df.columns:\n", "        total_records = len(claims_df)\n", "        non_null_records = claims_df[field].notna().sum()\n", "        completeness = (non_null_records / total_records) * 100\n", "        \n", "        quality_metrics[field] = {\n", "            'completeness': completeness,\n", "            'unique_values': claims_df[field].nunique(),\n", "            'data_type': str(claims_df[field].dtype)\n", "        }\n", "        \n", "        status = \"✅\" if completeness >= 95 else \"⚠️\" if completeness >= 80 else \"❌\"\n", "        print(f\"  {status} {field}: {completeness:.1f}% complete, {claims_df[field].nunique():,} unique values\")\n", "\n", "# Assess mapping coverage potential\n", "print(\"\\n🎯 MAPPING COVERAGE ASSESSMENT:\")\n", "\n", "# CPT codes coverage\n", "cpt_codes = claims_df[claims_df['code_activity'].str.match(r'^\\d{5}$', na=False)]\n", "cpt_coverage = len(cpt_codes) / len(claims_df) * 100\n", "print(f\"  CPT codes: {cpt_coverage:.1f}% of activities\")\n", "\n", "# Drug codes coverage\n", "drug_codes = claims_df[claims_df['code_activity'].str.contains(r'[A-Z]\\d+-\\d+', na=False)]\n", "drug_coverage = len(drug_codes) / len(claims_df) * 100\n", "print(f\"  Drug codes: {drug_coverage:.1f}% of activities\")\n", "\n", "# Date completeness\n", "date_completeness = claims_df['encounter_start_date'].notna().sum() / len(claims_df) * 100\n", "print(f\"  Date information: {date_completeness:.1f}% complete\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Phase 2 Learning Summary\n", "\n", "Document your data analysis insights."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 📝 My Phase 2 Learning Notes\n", "\n", "**Clinical Data Insights:**\n", "1. \n", "2. \n", "3. \n", "\n", "**Medical Code Patterns:**\n", "1. \n", "2. \n", "3. \n", "\n", "**Data Quality Findings:**\n", "1. \n", "2. \n", "3. \n", "\n", "**Mapping Strategy Decisions:**\n", "1. \n", "2. \n", "3. "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Save Phase 2 analysis insights\n", "phase2_insights = {\n", "    'total_records': len(claims_df),\n", "    'unique_patients': claims_df['aio_patient_id'].nunique(),\n", "    'unique_encounters': claims_df['case'].nunique() if 'case' in claims_df.columns else 0,\n", "    'unique_codes': claims_df['code_activity'].nunique() if 'code_activity' in claims_df.columns else 0,\n", "    'cpt_coverage': cpt_coverage,\n", "    'drug_coverage': drug_coverage,\n", "    'date_completeness': date_completeness,\n", "    'quality_metrics': quality_metrics,\n", "    'completion_timestamp': pd.Timestamp.now().isoformat()\n", "}\n", "\n", "print(\"✅ Phase 2 Complete!\")\n", "print(f\"📊 Analyzed {phase2_insights['total_records']:,} records\")\n", "print(f\"👥 Covering {phase2_insights['unique_patients']:,} patients\")\n", "print(f\"🏥 Across {phase2_insights['unique_encounters']:,} encounters\")\n", "print(f\"💊 With {phase2_insights['unique_codes']:,} unique medical codes\")\n", "print(\"🎯 Ready for Phase 3: Implementation Excellence\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### ✅ Phase 2 Completion Checklist\n", "\n", "Before moving to Phase 3, ensure you can answer YES to these questions:\n", "\n", "- [ ] Do I understand the clinical context of the dataset?\n", "- [ ] Have I researched the medical codes and their meanings?\n", "- [ ] Have I identified data quality challenges and solutions?\n", "- [ ] Do I have a clear mapping strategy for each OMOP domain?\n", "\n", "---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 🛠️ Phase 3: Implementation Excellence (10-12 hours)\n", "*\"Building Production-Quality ETL\"*\n", "\n", "## Learning Objectives\n", "- Master SQLAlchemy for healthcare data modeling\n", "- Implement robust data transformation patterns\n", "- Understand OMOP data validation requirements\n", "- Create maintainable, documented code"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Sub-Phase 3.1: OMOP Data Models (3-4 hours)\n", "\n", "Let's implement SQLAlchemy models for our OMOP tables."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# SQLAlchemy models for OMOP tables\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, Numeric, Text, ForeignKey\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sqlalchemy.orm import relationship, sessionmaker\n", "from datetime import datetime, date\n", "\n", "Base = declarative_base()\n", "\n", "print(\"🏗️ IMPLEMENTING OMOP DATA MODELS\")\n", "print(\"=\" * 60)\n", "\n", "class Person(Base):\n", "    \"\"\"\n", "    OMOP Person table - represents unique patients.\n", "    \n", "    Learning notes:\n", "    - person_id: Unique identifier for each person\n", "    - gender_concept_id: Standardized gender concept (8507=Male, 8532=Female)\n", "    - year_of_birth: Birth year for age calculations\n", "    - race/ethnicity: Demographic information using standard concepts\n", "    \"\"\"\n", "    __tablename__ = 'person'\n", "    \n", "    person_id = Column(Integer, primary_key=True)\n", "    gender_concept_id = Column(Integer, nullable=False)  # Standard concept for gender\n", "    year_of_birth = Column(Integer)  # Birth year\n", "    month_of_birth = Column(Integer)  # Birth month (optional)\n", "    day_of_birth = Column(Integer)  # Birth day (optional)\n", "    birth_datetime = Column(DateTime)  # Full birth datetime (optional)\n", "    race_concept_id = Column(Integer, nullable=False)  # Standard race concept\n", "    ethnicity_concept_id = Column(Integer, nullable=False)  # Standard ethnicity concept\n", "    location_id = Column(Integer)  # Geographic location\n", "    provider_id = Column(Integer)  # Primary care provider\n", "    care_site_id = Column(Integer)  # Primary care site\n", "    person_source_value = Column(String(50))  # Original patient identifier\n", "    gender_source_value = Column(String(50))  # Original gender value\n", "    gender_source_concept_id = Column(Integer)  # Source gender concept\n", "    race_source_value = Column(String(50))  # Original race value\n", "    race_source_concept_id = Column(Integer)  # Source race concept\n", "    ethnicity_source_value = Column(String(50))  # Original ethnicity value\n", "    ethnicity_source_concept_id = Column(Integer)  # Source ethnicity concept\n", "\n", "print(\"✅ Person model defined\")\n", "\n", "class VisitOccurrence(Base):\n", "    \"\"\"\n", "    OMOP Visit_Occurrence table - represents healthcare encounters.\n", "    \n", "    Learning notes:\n", "    - visit_occurrence_id: Unique identifier for each visit\n", "    - person_id: Links to Person table\n", "    - visit_concept_id: Type of visit (9201=Inpatient, 9202=Outpatient)\n", "    - visit_start/end_date: Duration of the encounter\n", "    \"\"\"\n", "    __tablename__ = 'visit_occurrence'\n", "    \n", "    visit_occurrence_id = Column(Integer, primary_key=True)\n", "    person_id = <PERSON><PERSON>n(<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>('person.person_id'), nullable=False)\n", "    visit_concept_id = Column(Integer, nullable=False)  # Standard visit type concept\n", "    visit_start_date = Column(Date, nullable=False)  # Visit start date\n", "    visit_start_datetime = Column(DateTime)  # Visit start datetime\n", "    visit_end_date = Column(Date, nullable=False)  # Visit end date\n", "    visit_end_datetime = Column(DateTime)  # Visit end datetime\n", "    visit_type_concept_id = Column(Integer, nullable=False)  # How visit was recorded\n", "    provider_id = Column(Integer)  # Primary provider for visit\n", "    care_site_id = Column(Integer)  # Care site where visit occurred\n", "    visit_source_value = Column(String(50))  # Original visit identifier\n", "    visit_source_concept_id = Column(Integer)  # Source visit concept\n", "    admitted_from_concept_id = Column(Integer)  # Where patient came from\n", "    admitted_from_source_value = Column(String(50))  # Original admission source\n", "    discharged_to_concept_id = Column(Integer)  # Where patient went\n", "    discharged_to_source_value = Column(String(50))  # Original discharge destination\n", "    preceding_visit_occurrence_id = Column(Integer)  # Previous visit\n", "    \n", "    # Relationship to Person\n", "    person = relationship(\"Person\")\n", "\n", "print(\"✅ Visit_Occurrence model defined\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 🤔 Reflection Questions - Data Modeling\n", "\n", "Think about the models you've just implemented:"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Question 11**: Why does OMOP use concept_ids instead of storing text values directly?\n", "\n", "*Your answer:*\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Question 12**: What is the purpose of having both source_value and concept_id fields?\n", "\n", "*Your answer:*\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Sub-Phase 3.2: ETL Implementation (4-5 hours)\n", "\n", "Now let's implement the transformation logic."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ETL Implementation with detailed learning\n", "print(\"🔄 ETL IMPLEMENTATION\")\n", "print(\"=\" * 60)\n", "\n", "def transform_claims_to_omop(claims_df, engine):\n", "    \"\"\"\n", "    Transform Abu Dhabi claims data to OMOP format.\n", "    \n", "    Learning approach:\n", "    1. Extract unique persons from claims\n", "    2. Create visit occurrences from encounters\n", "    3. Map procedures and drugs to OMOP concepts\n", "    4. Validate and load data with quality checks\n", "    \"\"\"\n", "    \n", "    print(\"🔄 Starting ETL transformation...\")\n", "    \n", "    # Step 1: Extract unique persons\n", "    print(\"\\n👥 Step 1: Extracting unique persons...\")\n", "    unique_patients = claims_df['aio_patient_id'].unique()\n", "    print(f\"   Found {len(unique_patients):,} unique patients\")\n", "    \n", "    persons_data = []\n", "    for i, patient_id in enumerate(unique_patients, 1):\n", "        person_record = {\n", "            'person_id': i,\n", "            'gender_concept_id': 0,  # Unknown gender - would need demographic data\n", "            'year_of_birth': None,   # Not available in claims\n", "            'race_concept_id': 0,    # Unknown race\n", "            'ethnicity_concept_id': 0,  # Unknown ethnicity\n", "            'person_source_value': str(patient_id)\n", "        }\n", "        persons_data.append(person_record)\n", "    \n", "    print(f\"   ✅ Created {len(persons_data):,} person records\")\n", "    \n", "    # Step 2: Extract visit occurrences\n", "    print(\"\\n🏥 Step 2: Extracting visit occurrences...\")\n", "    \n", "    # Group by case to create visits\n", "    visit_groups = claims_df.groupby(['case', 'aio_patient_id']).agg({\n", "        'encounter_start_date': 'first',\n", "        'encounter_end_date': 'first',\n", "        'case_type': 'first'\n", "    }).reset_index()\n", "    \n", "    print(f\"   Found {len(visit_groups):,} unique visits\")\n", "    \n", "    # Create person_id mapping\n", "    patient_to_person = {patient_id: i+1 for i, patient_id in enumerate(unique_patients)}\n", "    \n", "    visits_data = []\n", "    for i, visit in visit_groups.iterrows():\n", "        visit_record = {\n", "            'visit_occurrence_id': i + 1,\n", "            'person_id': patient_to_person[visit['aio_patient_id']],\n", "            'visit_concept_id': 9202,  # Outpatient visit (default)\n", "            'visit_start_date': pd.to_datetime(visit['encounter_start_date']).date(),\n", "            'visit_end_date': pd.to_datetime(visit['encounter_end_date']).date(),\n", "            'visit_type_concept_id': 32817,  # EHR record\n", "            'visit_source_value': str(visit['case'])\n", "        }\n", "        visits_data.append(visit_record)\n", "    \n", "    print(f\"   ✅ Created {len(visits_data):,} visit records\")\n", "    \n", "    return persons_data, visits_data\n", "\n", "# Execute transformation\n", "if engine and 'claims_df' in locals():\n", "    try:\n", "        persons_data, visits_data = transform_claims_to_omop(claims_df, engine)\n", "        print(\"\\n✅ ETL transformation completed successfully!\")\n", "    except Exception as e:\n", "        print(f\"\\n❌ ETL transformation failed: {str(e)}\")\nelse:\n", "    print(\"❌ Cannot run ETL - missing database connection or data\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Sub-Phase 3.3: Data Loading (2-3 hours)\n", "\n", "Load the transformed data into the OMOP database."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Data loading with validation\n", "print(\"📥 DATA LOADING TO OMOP DATABASE\")\n", "print(\"=\" * 60)\n", "\n", "def load_data_to_omop(persons_data, visits_data, engine):\n", "    \"\"\"\n", "    Load transformed data to OMOP database with validation.\n", "    \n", "    Learning focus:\n", "    - Transaction management for data integrity\n", "    - Batch loading for performance\n", "    - Error handling and rollback\n", "    - Data validation before commit\n", "    \"\"\"\n", "    \n", "    from sqlalchemy.orm import sessionmaker\n", "    \n", "    Session = sessionmaker(bind=engine)\n", "    session = Session()\n", "    \n", "    try:\n", "        print(\"🔄 Starting data loading transaction...\")\n", "        \n", "        # Load persons\n", "        print(f\"\\n👥 Loading {len(persons_data):,} person records...\")\n", "        \n", "        for person_data in persons_data:\n", "            person = Person(**person_data)\n", "            session.add(person)\n", "        \n", "        # Flush to get person IDs\n", "        session.flush()\n", "        print(\"   ✅ Person records staged\")\n", "        \n", "        # Load visits\n", "        print(f\"\\n🏥 Loading {len(visits_data):,} visit records...\")\n", "        \n", "        for visit_data in visits_data:\n", "            visit = VisitOccurrence(**visit_data)\n", "            session.add(visit)\n", "        \n", "        session.flush()\n", "        print(\"   ✅ Visit records staged\")\n", "        \n", "        # Validate data before commit\n", "        print(\"\\n🔍 Validating data integrity...\")\n", "        \n", "        # Check for orphaned visits\n", "        person_ids = {p['person_id'] for p in persons_data}\n", "        visit_person_ids = {v['person_id'] for v in visits_data}\n", "        orphaned_visits = visit_person_ids - person_ids\n", "        \n", "        if orphaned_visits:\n", "            raise ValueError(f\"Found {len(orphaned_visits)} orphaned visits\")\n", "        \n", "        print(\"   ✅ Data integrity validated\")\n", "        \n", "        # Commit transaction\n", "        session.commit()\n", "        print(\"\\n✅ Data loading completed successfully!\")\n", "        \n", "        return True\n", "        \n", "    except Exception as e:\n", "        session.rollback()\n", "        print(f\"\\n❌ Data loading failed: {str(e)}\")\n", "        print(\"🔄 Transaction rolled back\")\n", "        return False\n", "        \n", "    finally:\n", "        session.close()\n", "\n", "# Execute data loading\n", "if engine and 'persons_data' in locals() and 'visits_data' in locals():\n", "    loading_success = load_data_to_omop(persons_data, visits_data, engine)\n", "    \n", "    if loading_success:\n", "        print(\"🎉 ETL pipeline completed successfully!\")\n", "    else:\n", "        print(\"⚠️ ETL pipeline completed with errors\")\nelse:\n", "    print(\"❌ Cannot load data - missing prerequisites\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Phase 3 Learning Summary\n", "\n", "Document your implementation insights."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 📝 My Phase 3 Learning Notes\n", "\n", "**SQLAlchemy Modeling Insights:**\n", "1. \n", "2. \n", "3. \n", "\n", "**ETL Implementation Patterns:**\n", "1. \n", "2. \n", "3. \n", "\n", "**Data Validation Approaches:**\n", "1. \n", "2. \n", "3. \n", "\n", "**Technical Challenges Overcome:**\n", "1. \n", "2. \n", "3. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["### ✅ Phase 3 Completion Checklist\n", "\n", "Before moving to Phase 4, ensure you can answer YES to these questions:\n", "\n", "- [ ] Have I implemented complete OMOP data models?\n", "- [ ] Is my ETL logic robust and well-documented?\n", "- [ ] Does my code include proper error handling and validation?\n", "- [ ] Can I explain each transformation decision?\n", "\n", "---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# ✅ Phase 4: Validation & Mastery (4-6 hours)\n", "*\"Ensuring Quality and Consolidating Knowledge\"*\n", "\n", "## Learning Objectives\n", "- Master healthcare data quality assessment\n", "- Understand OMOP validation standards\n", "- Synthesize learning for future application\n", "- Document patterns for team knowledge sharing"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Comprehensive Quality Control\n", "\n", "Let's validate our OMOP implementation with clinical perspective."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Comprehensive quality control\n", "print(\"🔍 COMPREHENSIVE QUALITY CONTROL\")\n", "print(\"=\" * 60)\n", "\n", "def run_quality_control(engine):\n", "    \"\"\"\n", "    Run comprehensive quality control checks on OMOP data.\n", "    \n", "    Learning focus:\n", "    - Clinical data validation patterns\n", "    - OMOP-specific quality metrics\n", "    - Interpretation of results in healthcare context\n", "    \"\"\"\n", "    \n", "    qc_results = {}\n", "    \n", "    with engine.connect() as conn:\n", "        print(\"📊 Basic Record Counts:\")\n", "        \n", "        # Person counts\n", "        person_count = conn.execute(text(\"SELECT COUNT(*) FROM person\")).fetchone()[0]\n", "        qc_results['person_count'] = person_count\n", "        print(f\"   👥 Persons: {person_count:,}\")\n", "        \n", "        # Visit counts\n", "        visit_count = conn.execute(text(\"SELECT COUNT(*) FROM visit_occurrence\")).fetchone()[0]\n", "        qc_results['visit_count'] = visit_count\n", "        print(f\"   🏥 Visits: {visit_count:,}\")\n", "        \n", "        # Calculate visits per person\n", "        if person_count > 0:\n", "            visits_per_person = visit_count / person_count\n", "            qc_results['visits_per_person'] = visits_per_person\n", "            print(f\"   📈 Visits per person: {visits_per_person:.1f}\")\n", "        \n", "        print(\"\\n🔍 Data Quality Checks:\")\n", "        \n", "        # Check for orphaned visits\n", "        orphaned_visits = conn.execute(text(\"\"\"\n", "            SELECT COUNT(*) FROM visit_occurrence v \n", "            LEFT JOIN person p ON v.person_id = p.person_id \n", "            WHERE p.person_id IS NULL\n", "        \"\"\")).fetchone()[0]\n", "        \n", "        qc_results['orphaned_visits'] = orphaned_visits\n", "        status = \"✅\" if orphaned_visits == 0 else \"❌\"\n", "        print(f\"   {status} Orphaned visits: {orphaned_visits}\")\n", "        \n", "        # Check date consistency\n", "        invalid_dates = conn.execute(text(\"\"\"\n", "            SELECT COUNT(*) FROM visit_occurrence \n", "            WHERE visit_start_date > visit_end_date\n", "        \"\"\")).fetchone()[0]\n", "        \n", "        qc_results['invalid_dates'] = invalid_dates\n", "        status = \"✅\" if invalid_dates == 0 else \"❌\"\n", "        print(f\"   {status} Invalid date ranges: {invalid_dates}\")\n", "        \n", "        # Check for required fields\n", "        null_person_ids = conn.execute(text(\"\"\"\n", "            SELECT COUNT(*) FROM person WHERE person_id IS NULL\n", "        \"\"\")).fetchone()[0]\n", "        \n", "        qc_results['null_person_ids'] = null_person_ids\n", "        status = \"✅\" if null_person_ids == 0 else \"❌\"\n", "        print(f\"   {status} Null person IDs: {null_person_ids}\")\n", "        \n", "        print(\"\\n📈 Clinical Metrics:\")\n", "        \n", "        # Visit duration analysis\n", "        avg_visit_duration = conn.execute(text(\"\"\"\n", "            SELECT AVG(visit_end_date - visit_start_date) \n", "            FROM visit_occurrence\n", "        \"\"\")).fetchone()[0]\n", "        \n", "        if avg_visit_duration is not None:\n", "            qc_results['avg_visit_duration_days'] = float(avg_visit_duration)\n", "            print(f\"   📅 Average visit duration: {avg_visit_duration:.1f} days\")\n", "        \n", "        # Date range analysis\n", "        date_range = conn.execute(text(\"\"\"\n", "            SELECT MIN(visit_start_date), MAX(visit_end_date) \n", "            FROM visit_occurrence\n", "        \"\"\")).fetchone()\n", "        \n", "        if date_range[0] and date_range[1]:\n", "            qc_results['date_range'] = {\n", "                'start': str(date_range[0]),\n", "                'end': str(date_range[1])\n", "            }\n", "            print(f\"   📅 Data date range: {date_range[0]} to {date_range[1]}\")\n", "    \n", "    return qc_results\n", "\n", "# Run quality control\n", "if engine:\n", "    try:\n", "        qc_results = run_quality_control(engine)\n", "        print(\"\\n✅ Quality control completed!\")\n", "    except Exception as e:\n", "        print(f\"\\n❌ Quality control failed: {str(e)}\")\n", "        qc_results = {}\nelse:\n", "    print(\"❌ Cannot run quality control - no database connection\")\n", "    qc_results = {}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Final Learning Synthesis\n", "\n", "Consolidate all your OMOP learning from this journey."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 📝 Complete Learning Journey Summary\n", "\n", "**Phase 0 - Theoretical Foundation:**\n", "- Key insight 1:\n", "- Key insight 2:\n", "- Key insight 3:\n", "\n", "**Phase 1 - Infrastructure:**\n", "- Key insight 1:\n", "- Key insight 2:\n", "- Key insight 3:\n", "\n", "**Phase 2 - Data Analysis:**\n", "- Key insight 1:\n", "- Key insight 2:\n", "- Key insight 3:\n", "\n", "**Phase 3 - Implementation:**\n", "- Key insight 1:\n", "- Key insight 2:\n", "- Key insight 3:\n", "\n", "**Phase 4 - Validation:**\n", "- Key insight 1:\n", "- Key insight 2:\n", "- Key insight 3:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Final project summary\n", "print(\"🎉 ABU DHABI CLAIMS OMOP MVP - COMPLETION SUMMARY\")\n", "print(\"=\" * 70)\n", "\n", "# Compile final metrics\n", "final_summary = {\n", "    'project_completion': pd.Timestamp.now().isoformat(),\n", "    'phases_completed': 4,\n", "    'learning_objectives_met': True,\n", "    'technical_deliverables': {\n", "        'omop_database': engine is not None,\n", "        'data_analysis': 'claims_df' in locals(),\n", "        'etl_implementation': 'persons_data' in locals(),\n", "        'quality_control': 'qc_results' in locals()\n", "    },\n", "    'data_metrics': {\n", "        'source_records': len(claims_df) if 'claims_df' in locals() else 0,\n", "        'persons_created': qc_results.get('person_count', 0),\n", "        'visits_created': qc_results.get('visit_count', 0),\n", "        'data_quality_score': 'Excellent' if qc_results.get('orphaned_visits', 1) == 0 else 'Needs improvement'\n", "    }\n", "}\n", "\n", "print(f\"📅 Project completed: {final_summary['project_completion']}\")\n", "print(f\"✅ Phases completed: {final_summary['phases_completed']}/4\")\n", "print(f\"📊 Source records processed: {final_summary['data_metrics']['source_records']:,}\")\n", "print(f\"👥 OMOP persons created: {final_summary['data_metrics']['persons_created']:,}\")\n", "print(f\"🏥 OMOP visits created: {final_summary['data_metrics']['visits_created']:,}\")\n", "print(f\"🎯 Data quality: {final_summary['data_metrics']['data_quality_score']}\")\n", "\n", "print(\"\\n🚀 NEXT STEPS FOR MAIN PROJECT:\")\n", "print(\"1. Apply learned patterns to FHIR-to-OMOP pipeline\")\n", "print(\"2. Implement vocabulary management system\")\n", "print(\"3. Scale ETL patterns for larger datasets\")\n", "print(\"4. Enhance data quality validation framework\")\n", "print(\"5. Document best practices for team\")\n", "\n", "print(\"\\n🎓 CONGRATULATIONS!\")\n", "print(\"You have successfully completed the Abu Dhabi Claims OMOP MVP\")\n", "print(\"and gained deep understanding of OMOP CDM implementation!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### ✅ Final Completion Checklist\n", "\n", "Verify your complete learning journey:\n", "\n", "**Technical Mastery:**\n", "- [ ] Can I explain OMOP CDM architecture and purpose?\n", "- [ ] Do I understand vocabulary management and concept mapping?\n", "- [ ] Can I implement robust ETL patterns for healthcare data?\n", "- [ ] Do I know how to validate data quality in clinical contexts?\n", "\n", "**Project Deliverables:**\n", "- [ ] OMOP database deployed and functional\n", "- [ ] Claims data analyzed and understood\n", "- [ ] ETL pipeline implemented and tested\n", "- [ ] Quality control completed with good results\n", "\n", "**Knowledge Transfer:**\n", "- [ ] Learning notes documented for each phase\n", "- [ ] Patterns identified for main project application\n", "- [ ] Best practices documented for team sharing\n", "- [ ] Ready to mentor others in OMOP implementation\n", "\n", "---\n", "\n", "## 🎯 Mission Accomplished!\n", "\n", "You have successfully completed the Abu Dhabi Claims OMOP MVP with a deep, pedagogical understanding of OMOP CDM. The knowledge and patterns you've developed here will directly accelerate the main FHIR-OMOP project development.\n", "\n", "**Well done! 🎉**"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}