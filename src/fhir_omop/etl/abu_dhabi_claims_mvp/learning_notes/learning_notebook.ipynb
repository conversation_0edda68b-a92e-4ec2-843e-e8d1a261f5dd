# Import essential libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# Set up plotting
plt.style.use('default')
sns.set_palette("husl")

print("✅ Environment setup complete!")
print("📚 Ready to begin OMOP learning journey")

# Load the Abu Dhabi claims dataset
data_path = "../../../../../data/real_test_datasets/claim_anonymized.csv"

try:
    claims_df = pd.read_csv(data_path)
    print(f"✅ Successfully loaded claims data")
    print(f"📊 Dataset shape: {claims_df.shape}")
    print(f"📋 Columns: {len(claims_df.columns)}")
except FileNotFoundError:
    print("❌ Claims data file not found. Please check the path.")
    print(f"🔍 Looking for: {data_path}")

# Basic dataset overview
print("🏥 ABU DHABI CLAIMS DATASET OVERVIEW")
print("=" * 50)
print(f"Total records: {len(claims_df):,}")
print(f"Total columns: {len(claims_df.columns)}")
print(f"Memory usage: {claims_df.memory_usage(deep=True).sum() / 1024**2:.2f} MB")
print("\n📋 Column Names:")
for i, col in enumerate(claims_df.columns, 1):
    print(f"{i:2d}. {col}")

# Sample data examination
print("🔍 SAMPLE RECORDS")
print("=" * 50)
display(claims_df.head(3))

# Explore data diversity - the interoperability challenge
print("🌍 DATA DIVERSITY ANALYSIS")
print("=" * 50)

# Unique values in key fields
key_fields = ['provider_id', 'institution_name', 'case_type', 'code_activity', 'type_activity']

for field in key_fields:
    if field in claims_df.columns:
        unique_count = claims_df[field].nunique()
        print(f"{field}: {unique_count} unique values")
        
        # Show sample values
        sample_values = claims_df[field].value_counts().head(3)
        print(f"  Top values: {list(sample_values.index)}")
        print()

# Let's map our claims data to OMOP domains conceptually
print("🗺️ CONCEPTUAL MAPPING: CLAIMS → OMOP DOMAINS")
print("=" * 60)

# Analyze what OMOP domains our data might map to
domain_mapping = {
    'Person Domain': ['aio_patient_id', 'unique_id'],
    'Visit Domain': ['case', 'encounter_start_date', 'encounter_end_date', 'case_type'],
    'Procedure Domain': ['code_activity', 'activity_desc', 'type_activity'],
    'Drug Domain': [],  # We'll identify these
    'Measurement Domain': [],  # We'll identify these
    'Provider Domain': ['provider_id', 'clinician', 'clinician_name'],
    'Payer Domain': ['payer_id', 'payer_id_desc', 'insurance_plan_id']
}

for domain, fields in domain_mapping.items():
    print(f"\n{domain}:")
    available_fields = [f for f in fields if f in claims_df.columns]
    if available_fields:
        for field in available_fields:
            print(f"  ✅ {field}")
    else:
        print(f"  ❓ Fields to be identified")

# Analyze activity types and codes
print("🏥 CLINICAL ACTIVITY ANALYSIS")
print("=" * 50)

# Activity types
if 'type_activity' in claims_df.columns:
    activity_types = claims_df['type_activity'].value_counts()
    print("Activity Types:")
    for activity_type, count in activity_types.head(10).items():
        print(f"  {activity_type}: {count:,} records")

print("\n" + "=" * 50)

# Sample activity codes and descriptions
if 'code_activity' in claims_df.columns and 'activity_desc' in claims_df.columns:
    print("\nSample Medical Codes:")
    sample_codes = claims_df[['code_activity', 'activity_desc']].drop_duplicates().head(10)
    for _, row in sample_codes.iterrows():
        print(f"  {row['code_activity']}: {row['activity_desc'][:60]}...")

# Identify potential drug codes
print("💊 DRUG CODE IDENTIFICATION")
print("=" * 50)

# Look for drug-like patterns in activity codes
if 'code_activity' in claims_df.columns:
    # Drug codes often contain letters and numbers in specific patterns
    drug_pattern_mask = claims_df['code_activity'].str.contains(r'[A-Z]\d+-\d+', na=False)
    potential_drugs = claims_df[drug_pattern_mask]
    
    if len(potential_drugs) > 0:
        print(f"Found {len(potential_drugs)} potential drug records")
        print("\nSample drug codes:")
        drug_samples = potential_drugs[['code_activity', 'activity_desc']].drop_duplicates().head(5)
        for _, row in drug_samples.iterrows():
            print(f"  {row['code_activity']}: {row['activity_desc']}")
    else:
        print("No obvious drug code patterns found")
        print("Will need to investigate activity types further")

# Save your Phase 0 insights
phase0_insights = {
    'dataset_shape': claims_df.shape,
    'unique_patients': claims_df['aio_patient_id'].nunique() if 'aio_patient_id' in claims_df.columns else 'Unknown',
    'date_range': {
        'start': claims_df['encounter_start_date'].min() if 'encounter_start_date' in claims_df.columns else 'Unknown',
        'end': claims_df['encounter_start_date'].max() if 'encounter_start_date' in claims_df.columns else 'Unknown'
    },
    'activity_types': claims_df['type_activity'].value_counts().to_dict() if 'type_activity' in claims_df.columns else {},
    'completion_timestamp': pd.Timestamp.now().isoformat()
}

print("✅ Phase 0 Complete!")
print(f"📊 Analyzed {phase0_insights['dataset_shape'][0]:,} records")
print(f"👥 Covering {phase0_insights['unique_patients']} unique patients")
print("🎯 Ready for Phase 1: Infrastructure Deep Dive")

# Database connection setup
import sqlalchemy as sa
import psycopg2
from sqlalchemy import create_engine, text
import os

# Database configuration for Abu Dhabi OMOP
DB_CONFIG = {
    'host': 'localhost',
    'port': '5433',  # Different from FHIR server to avoid conflicts
    'database': 'omop_cdm_abu_dhabi',
    'username': 'omop_user',
    'password': 'secure_password',
    'schema': 'public'
}

# Create connection string
connection_string = f"postgresql://{DB_CONFIG['username']}:{DB_CONFIG['password']}@{DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}"

print("🔧 Database Configuration:")
print(f"  Host: {DB_CONFIG['host']}")
print(f"  Port: {DB_CONFIG['port']}")
print(f"  Database: {DB_CONFIG['database']}")
print(f"  Schema: {DB_CONFIG['schema']}")
print("\n⚠️  Note: Make sure your OMOP database is running before executing the next cell!")

# Test database connection
try:
    engine = create_engine(connection_string)
    
    # Test connection
    with engine.connect() as conn:
        result = conn.execute(text("SELECT version();"))
        version = result.fetchone()[0]
        
    print("✅ Database connection successful!")
    print(f"📊 PostgreSQL version: {version.split(',')[0]}")
    
except Exception as e:
    print("❌ Database connection failed!")
    print(f"Error: {str(e)}")
    print("\n🔧 Troubleshooting steps:")
    print("1. Ensure PostgreSQL container is running")
    print("2. Check connection parameters")
    print("3. Verify database and user exist")
    engine = None

# Explore OMOP table structure
if engine:
    print("🏗️ OMOP CDM TABLE STRUCTURE")
    print("=" * 60)
    
    # Get all tables
    tables_query = """
    SELECT table_name 
    FROM information_schema.tables 
    WHERE table_schema = 'public' 
    ORDER BY table_name;
    """
    
    with engine.connect() as conn:
        tables_result = conn.execute(text(tables_query))
        tables = [row[0] for row in tables_result.fetchall()]
    
    # Categorize tables by domain
    table_categories = {
        'Clinical Data': ['person', 'visit_occurrence', 'condition_occurrence', 
                         'procedure_occurrence', 'drug_exposure', 'measurement', 
                         'observation', 'death'],
        'Vocabulary': ['concept', 'vocabulary', 'domain', 'concept_class', 
                      'concept_relationship', 'relationship', 'concept_synonym', 
                      'concept_ancestor', 'source_to_concept_map', 'drug_strength'],
        'Healthcare System': ['provider', 'care_site', 'location'],
        'Health Economics': ['payer_plan_period', 'cost'],
        'Metadata': ['cdm_source', 'metadata']
    }
    
    for category, expected_tables in table_categories.items():
        print(f"\n{category}:")
        found_tables = [t for t in expected_tables if t in tables]
        missing_tables = [t for t in expected_tables if t not in tables]
        
        for table in found_tables:
            print(f"  ✅ {table}")
        for table in missing_tables:
            print(f"  ❌ {table} (missing)")
    
    print(f"\n📊 Total tables found: {len(tables)}")
else:
    print("❌ Cannot explore schema - no database connection")

# Examine key table structures
if engine:
    key_tables = ['person', 'visit_occurrence', 'concept']
    
    for table_name in key_tables:
        print(f"\n🔍 {table_name.upper()} TABLE STRUCTURE")
        print("=" * 50)
        
        # Get column information
        columns_query = f"""
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns 
        WHERE table_name = '{table_name}' 
        AND table_schema = 'public'
        ORDER BY ordinal_position;
        """
        
        try:
            with engine.connect() as conn:
                columns_df = pd.read_sql(columns_query, conn)
            
            if not columns_df.empty:
                for _, row in columns_df.iterrows():
                    nullable = "NULL" if row['is_nullable'] == 'YES' else "NOT NULL"
                    default = f" DEFAULT {row['column_default']}" if row['column_default'] else ""
                    print(f"  {row['column_name']:<25} {row['data_type']:<15} {nullable}{default}")
            else:
                print(f"  ❌ Table {table_name} not found or empty")
                
        except Exception as e:
            print(f"  ❌ Error examining {table_name}: {str(e)}")
else:
    print("❌ Cannot examine tables - no database connection")

# Explore vocabulary structure
if engine:
    print("📚 OMOP VOCABULARY EXPLORATION")
    print("=" * 60)
    
    # Check if vocabularies are loaded
    vocab_query = """
    SELECT vocabulary_id, vocabulary_name, vocabulary_version, vocabulary_concept_id
    FROM vocabulary 
    ORDER BY vocabulary_id;
    """
    
    try:
        with engine.connect() as conn:
            vocab_df = pd.read_sql(vocab_query, conn)
        
        if not vocab_df.empty:
            print(f"✅ Found {len(vocab_df)} vocabularies loaded")
            print("\nAvailable vocabularies:")
            for _, row in vocab_df.head(10).iterrows():
                print(f"  {row['vocabulary_id']:<15} {row['vocabulary_name']}")
            
            if len(vocab_df) > 10:
                print(f"  ... and {len(vocab_df) - 10} more")
        else:
            print("❌ No vocabularies found - need to load basic vocabularies")
            
    except Exception as e:
        print(f"❌ Error querying vocabularies: {str(e)}")
        print("This might indicate vocabularies are not yet loaded")
else:
    print("❌ Cannot explore vocabularies - no database connection")

# Explore concept structure (if vocabularies are loaded)
if engine:
    print("🔍 CONCEPT STRUCTURE EXPLORATION")
    print("=" * 60)
    
    concept_count_query = "SELECT COUNT(*) as concept_count FROM concept;"
    
    try:
        with engine.connect() as conn:
            result = conn.execute(text(concept_count_query))
            concept_count = result.fetchone()[0]
        
        print(f"📊 Total concepts in database: {concept_count:,}")
        
        if concept_count > 0:
            # Sample concepts by domain
            sample_concepts_query = """
            SELECT concept_id, concept_name, domain_id, vocabulary_id, concept_class_id
            FROM concept 
            WHERE standard_concept = 'S'
            ORDER BY concept_id 
            LIMIT 10;
            """
            
            with engine.connect() as conn:
                sample_df = pd.read_sql(sample_concepts_query, conn)
            
            print("\nSample standard concepts:")
            for _, row in sample_df.iterrows():
                print(f"  {row['concept_id']:<10} {row['domain_id']:<12} {row['concept_name'][:40]}...")
        else:
            print("⚠️  No concepts loaded - will need to load basic vocabularies")
            
    except Exception as e:
        print(f"❌ Error exploring concepts: {str(e)}")
else:
    print("❌ Cannot explore concepts - no database connection")

# Save Phase 1 infrastructure insights
if engine:
    # Test basic functionality
    with engine.connect() as conn:
        # Count tables
        tables_count = conn.execute(text("""
            SELECT COUNT(*) FROM information_schema.tables 
            WHERE table_schema = 'public'
        """)).fetchone()[0]
        
        # Count vocabularies
        try:
            vocab_count = conn.execute(text("SELECT COUNT(*) FROM vocabulary")).fetchone()[0]
        except:
            vocab_count = 0
        
        # Count concepts
        try:
            concept_count = conn.execute(text("SELECT COUNT(*) FROM concept")).fetchone()[0]
        except:
            concept_count = 0
    
    phase1_insights = {
        'database_connection': 'successful',
        'tables_count': tables_count,
        'vocabularies_loaded': vocab_count,
        'concepts_loaded': concept_count,
        'infrastructure_ready': tables_count > 20,  # Basic OMOP has ~40 tables
        'completion_timestamp': pd.Timestamp.now().isoformat()
    }
    
    print("✅ Phase 1 Complete!")
    print(f"🏗️  Database tables: {phase1_insights['tables_count']}")
    print(f"📚 Vocabularies loaded: {phase1_insights['vocabularies_loaded']}")
    print(f"🔍 Concepts available: {phase1_insights['concepts_loaded']:,}")
    print("🎯 Ready for Phase 2: Data Analysis Mastery")
    
else:
    print("❌ Phase 1 incomplete - database connection issues")
    print("🔧 Please resolve infrastructure setup before proceeding")

# Advanced data profiling setup
from ydata_profiling import ProfileReport
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots

print("🔍 COMPREHENSIVE CLINICAL DATA ANALYSIS")
print("=" * 60)

# Reload data if needed
if 'claims_df' not in locals() or claims_df is None:
    claims_df = pd.read_csv("../../../../data/real_test_datasets/claim_anonymized.csv")

print(f"📊 Dataset loaded: {claims_df.shape[0]:,} records, {claims_df.shape[1]} columns")
print(f"📅 Date range: {claims_df['encounter_start_date'].min()} to {claims_df['encounter_start_date'].max()}")
print(f"👥 Unique patients: {claims_df['aio_patient_id'].nunique():,}")

# Clinical encounter analysis
print("🏥 CLINICAL ENCOUNTER PATTERNS")
print("=" * 50)

# Encounter types analysis
encounter_types = claims_df['case_type'].value_counts()
print("\nEncounter Types:")
for enc_type, count in encounter_types.items():
    percentage = (count / len(claims_df)) * 100
    print(f"  {enc_type}: {count:,} ({percentage:.1f}%)")

# Activity types analysis
if 'type_activity' in claims_df.columns:
    activity_types = claims_df['type_activity'].value_counts()
    print("\nActivity Types:")
    for act_type, count in activity_types.head(10).items():
        percentage = (count / len(claims_df)) * 100
        print(f"  {act_type}: {count:,} ({percentage:.1f}%)")

# Patient encounter frequency
patient_encounters = claims_df.groupby('aio_patient_id').size()
print(f"\n👥 Patient Encounter Statistics:")
print(f"  Average encounters per patient: {patient_encounters.mean():.1f}")
print(f"  Median encounters per patient: {patient_encounters.median():.1f}")
print(f"  Max encounters per patient: {patient_encounters.max()}")

# Medical codes deep dive
print("💊 MEDICAL CODES ANALYSIS")
print("=" * 50)

# Analyze activity codes patterns
if 'code_activity' in claims_df.columns:
    unique_codes = claims_df['code_activity'].nunique()
    total_activities = len(claims_df)
    
    print(f"📋 Total unique medical codes: {unique_codes:,}")
    print(f"📊 Total activities: {total_activities:,}")
    print(f"🔄 Code reuse ratio: {total_activities/unique_codes:.1f}x")
    
    # Most common codes
    top_codes = claims_df['code_activity'].value_counts().head(10)
    print("\n🔝 Most Common Medical Codes:")
    for code, count in top_codes.items():
        # Get description
        desc = claims_df[claims_df['code_activity'] == code]['activity_desc'].iloc[0]
        print(f"  {code}: {count:,} times - {desc[:50]}...")
    
    # Identify code patterns
    print("\n🔍 Code Pattern Analysis:")
    
    # CPT-like codes (5 digits)
    cpt_pattern = claims_df['code_activity'].str.match(r'^\d{5}$', na=False)
    cpt_count = cpt_pattern.sum()
    print(f"  CPT-like codes (5 digits): {cpt_count:,} ({cpt_count/len(claims_df)*100:.1f}%)")
    
    # Drug-like codes (letters + numbers)
    drug_pattern = claims_df['code_activity'].str.contains(r'[A-Z]\d+-\d+', na=False)
    drug_count = drug_pattern.sum()
    print(f"  Drug-like codes: {drug_count:,} ({drug_count/len(claims_df)*100:.1f}%)")
    
    # Dental codes (5 digits starting with 2 or 7)
    dental_pattern = claims_df['code_activity'].str.match(r'^[27]\d{4}$', na=False)
    dental_count = dental_pattern.sum()
    print(f"  Dental-like codes: {dental_count:,} ({dental_count/len(claims_df)*100:.1f}%)")

# Generate comprehensive data profile
print("📊 GENERATING COMPREHENSIVE DATA PROFILE")
print("=" * 60)
print("⏳ This may take a few minutes for large datasets...")

# Create a sample for profiling if dataset is large
if len(claims_df) > 10000:
    sample_size = 5000
    profile_df = claims_df.sample(n=sample_size, random_state=42)
    print(f"📋 Using sample of {sample_size:,} records for profiling")
else:
    profile_df = claims_df
    print(f"📋 Profiling full dataset of {len(profile_df):,} records")

try:
    # Generate profile report
    profile = ProfileReport(
        profile_df, 
        title="Abu Dhabi Claims Dataset Profile",
        explorative=True,
        minimal=False
    )
    
    # Save the report
    profile_path = "eda/profile_report.html"
    profile.to_file(profile_path)
    
    print(f"✅ Profile report generated: {profile_path}")
    print("📖 Open the HTML file in your browser for detailed analysis")
    
except Exception as e:
    print(f"❌ Error generating profile: {str(e)}")
    print("💡 You can continue with manual analysis below")

# Research top CPT codes
print("🔬 MEDICAL CODE RESEARCH")
print("=" * 50)

# Extract CPT codes for research
cpt_codes = claims_df[claims_df['code_activity'].str.match(r'^\d{5}$', na=False)]
top_cpt = cpt_codes['code_activity'].value_counts().head(10)

print("🏥 Top CPT Codes for Research:")
cpt_research_list = []

for code, count in top_cpt.items():
    desc = cpt_codes[cpt_codes['code_activity'] == code]['activity_desc'].iloc[0]
    cpt_research_list.append({
        'code': code,
        'description': desc,
        'frequency': count,
        'percentage': (count / len(claims_df)) * 100
    })
    print(f"  {code}: {desc[:60]}... ({count:,} times)")

print("\n📚 Research these codes at:")
print("  - CMS CPT Code Lookup: https://www.cms.gov/medicare/coding/MedHCPCSGenInfo")
print("  - Shafafiya Dictionary: https://www.doh.gov.ae/en/Shafafiya/dictionary")
print("  - AAPC CPT Lookup: https://www.aapc.com/codes/")

# Research drug codes
print("💊 DRUG CODE RESEARCH")
print("=" * 50)

# Extract drug codes
drug_codes = claims_df[claims_df['code_activity'].str.contains(r'[A-Z]\d+-\d+', na=False)]

if len(drug_codes) > 0:
    top_drugs = drug_codes['code_activity'].value_counts().head(10)
    
    print("💊 Top Drug Codes for Research:")
    drug_research_list = []
    
    for code, count in top_drugs.items():
        desc = drug_codes[drug_codes['code_activity'] == code]['activity_desc'].iloc[0]
        drug_research_list.append({
            'code': code,
            'description': desc,
            'frequency': count,
            'percentage': (count / len(claims_df)) * 100
        })
        print(f"  {code}: {desc[:60]}... ({count:,} times)")
    
    print("\n🔍 Research these drugs at:")
    print("  - RxNorm Browser: https://mor.nlm.nih.gov/RxNav/")
    print("  - Drugs.com: https://www.drugs.com/")
    print("  - UAE Drug Index: Local pharmaceutical references")
else:
    print("❌ No obvious drug codes found with current pattern")
    print("🔍 May need to investigate other activity types")

# Design mapping strategy
print("🗺️ OMOP MAPPING STRATEGY DESIGN")
print("=" * 60)

# Analyze data for OMOP domain mapping
mapping_strategy = {
    'Person': {
        'source_fields': ['aio_patient_id', 'unique_id'],
        'challenges': ['No demographic data', 'Need to generate person records'],
        'approach': 'Create person records from unique patient IDs'
    },
    'Visit_Occurrence': {
        'source_fields': ['case', 'encounter_start_date', 'encounter_end_date', 'case_type'],
        'challenges': ['Multiple activities per visit', 'Visit type mapping'],
        'approach': 'Group activities by case ID and dates'
    },
    'Procedure_Occurrence': {
        'source_fields': ['code_activity', 'activity_desc', 'type_activity'],
        'challenges': ['CPT code mapping', 'Mixed activity types'],
        'approach': 'Map CPT codes to OMOP procedure concepts'
    },
    'Drug_Exposure': {
        'source_fields': ['code_activity', 'activity_desc'],
        'challenges': ['Local drug codes', 'Limited drug information'],
        'approach': 'Map drug codes to RxNorm or use concept_id=0'
    },
    'Provider': {
        'source_fields': ['provider_id', 'clinician', 'clinician_name'],
        'challenges': ['Provider specialty unknown'],
        'approach': 'Create provider records from unique identifiers'
    }
}

for domain, details in mapping_strategy.items():
    print(f"\n🏥 {domain}:")
    print(f"  Source fields: {', '.join(details['source_fields'])}")
    print(f"  Approach: {details['approach']}")
    print(f"  Challenges: {'; '.join(details['challenges'])}")

# Data quality assessment for mapping
print("\n📊 DATA QUALITY ASSESSMENT FOR MAPPING")
print("=" * 60)

quality_metrics = {}

# Check completeness of key fields
key_fields = ['aio_patient_id', 'case', 'code_activity', 'encounter_start_date']

for field in key_fields:
    if field in claims_df.columns:
        total_records = len(claims_df)
        non_null_records = claims_df[field].notna().sum()
        completeness = (non_null_records / total_records) * 100
        
        quality_metrics[field] = {
            'completeness': completeness,
            'unique_values': claims_df[field].nunique(),
            'data_type': str(claims_df[field].dtype)
        }
        
        status = "✅" if completeness >= 95 else "⚠️" if completeness >= 80 else "❌"
        print(f"  {status} {field}: {completeness:.1f}% complete, {claims_df[field].nunique():,} unique values")

# Assess mapping coverage potential
print("\n🎯 MAPPING COVERAGE ASSESSMENT:")

# CPT codes coverage
cpt_codes = claims_df[claims_df['code_activity'].str.match(r'^\d{5}$', na=False)]
cpt_coverage = len(cpt_codes) / len(claims_df) * 100
print(f"  CPT codes: {cpt_coverage:.1f}% of activities")

# Drug codes coverage
drug_codes = claims_df[claims_df['code_activity'].str.contains(r'[A-Z]\d+-\d+', na=False)]
drug_coverage = len(drug_codes) / len(claims_df) * 100
print(f"  Drug codes: {drug_coverage:.1f}% of activities")

# Date completeness
date_completeness = claims_df['encounter_start_date'].notna().sum() / len(claims_df) * 100
print(f"  Date information: {date_completeness:.1f}% complete")

# Save Phase 2 analysis insights
phase2_insights = {
    'total_records': len(claims_df),
    'unique_patients': claims_df['aio_patient_id'].nunique(),
    'unique_encounters': claims_df['case'].nunique() if 'case' in claims_df.columns else 0,
    'unique_codes': claims_df['code_activity'].nunique() if 'code_activity' in claims_df.columns else 0,
    'cpt_coverage': cpt_coverage,
    'drug_coverage': drug_coverage,
    'date_completeness': date_completeness,
    'quality_metrics': quality_metrics,
    'completion_timestamp': pd.Timestamp.now().isoformat()
}

print("✅ Phase 2 Complete!")
print(f"📊 Analyzed {phase2_insights['total_records']:,} records")
print(f"👥 Covering {phase2_insights['unique_patients']:,} patients")
print(f"🏥 Across {phase2_insights['unique_encounters']:,} encounters")
print(f"💊 With {phase2_insights['unique_codes']:,} unique medical codes")
print("🎯 Ready for Phase 3: Implementation Excellence")

# SQLAlchemy models for OMOP tables
from sqlalchemy import Column, Integer, String, Date, DateTime, Numeric, Text, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship, sessionmaker
from datetime import datetime, date

Base = declarative_base()

print("🏗️ IMPLEMENTING OMOP DATA MODELS")
print("=" * 60)

class Person(Base):
    """
    OMOP Person table - represents unique patients.
    
    Learning notes:
    - person_id: Unique identifier for each person
    - gender_concept_id: Standardized gender concept (8507=Male, 8532=Female)
    - year_of_birth: Birth year for age calculations
    - race/ethnicity: Demographic information using standard concepts
    """
    __tablename__ = 'person'
    
    person_id = Column(Integer, primary_key=True)
    gender_concept_id = Column(Integer, nullable=False)  # Standard concept for gender
    year_of_birth = Column(Integer)  # Birth year
    month_of_birth = Column(Integer)  # Birth month (optional)
    day_of_birth = Column(Integer)  # Birth day (optional)
    birth_datetime = Column(DateTime)  # Full birth datetime (optional)
    race_concept_id = Column(Integer, nullable=False)  # Standard race concept
    ethnicity_concept_id = Column(Integer, nullable=False)  # Standard ethnicity concept
    location_id = Column(Integer)  # Geographic location
    provider_id = Column(Integer)  # Primary care provider
    care_site_id = Column(Integer)  # Primary care site
    person_source_value = Column(String(50))  # Original patient identifier
    gender_source_value = Column(String(50))  # Original gender value
    gender_source_concept_id = Column(Integer)  # Source gender concept
    race_source_value = Column(String(50))  # Original race value
    race_source_concept_id = Column(Integer)  # Source race concept
    ethnicity_source_value = Column(String(50))  # Original ethnicity value
    ethnicity_source_concept_id = Column(Integer)  # Source ethnicity concept

print("✅ Person model defined")

class VisitOccurrence(Base):
    """
    OMOP Visit_Occurrence table - represents healthcare encounters.
    
    Learning notes:
    - visit_occurrence_id: Unique identifier for each visit
    - person_id: Links to Person table
    - visit_concept_id: Type of visit (9201=Inpatient, 9202=Outpatient)
    - visit_start/end_date: Duration of the encounter
    """
    __tablename__ = 'visit_occurrence'
    
    visit_occurrence_id = Column(Integer, primary_key=True)
    person_id = Column(Integer, ForeignKey('person.person_id'), nullable=False)
    visit_concept_id = Column(Integer, nullable=False)  # Standard visit type concept
    visit_start_date = Column(Date, nullable=False)  # Visit start date
    visit_start_datetime = Column(DateTime)  # Visit start datetime
    visit_end_date = Column(Date, nullable=False)  # Visit end date
    visit_end_datetime = Column(DateTime)  # Visit end datetime
    visit_type_concept_id = Column(Integer, nullable=False)  # How visit was recorded
    provider_id = Column(Integer)  # Primary provider for visit
    care_site_id = Column(Integer)  # Care site where visit occurred
    visit_source_value = Column(String(50))  # Original visit identifier
    visit_source_concept_id = Column(Integer)  # Source visit concept
    admitted_from_concept_id = Column(Integer)  # Where patient came from
    admitted_from_source_value = Column(String(50))  # Original admission source
    discharged_to_concept_id = Column(Integer)  # Where patient went
    discharged_to_source_value = Column(String(50))  # Original discharge destination
    preceding_visit_occurrence_id = Column(Integer)  # Previous visit
    
    # Relationship to Person
    person = relationship("Person")

print("✅ Visit_Occurrence model defined")

# ETL Implementation with detailed learning
print("🔄 ETL IMPLEMENTATION")
print("=" * 60)

def transform_claims_to_omop(claims_df, engine):
    """
    Transform Abu Dhabi claims data to OMOP format.
    
    Learning approach:
    1. Extract unique persons from claims
    2. Create visit occurrences from encounters
    3. Map procedures and drugs to OMOP concepts
    4. Validate and load data with quality checks
    """
    
    print("🔄 Starting ETL transformation...")
    
    # Step 1: Extract unique persons
    print("\n👥 Step 1: Extracting unique persons...")
    unique_patients = claims_df['aio_patient_id'].unique()
    print(f"   Found {len(unique_patients):,} unique patients")
    
    persons_data = []
    for i, patient_id in enumerate(unique_patients, 1):
        person_record = {
            'person_id': i,
            'gender_concept_id': 0,  # Unknown gender - would need demographic data
            'year_of_birth': None,   # Not available in claims
            'race_concept_id': 0,    # Unknown race
            'ethnicity_concept_id': 0,  # Unknown ethnicity
            'person_source_value': str(patient_id)
        }
        persons_data.append(person_record)
    
    print(f"   ✅ Created {len(persons_data):,} person records")
    
    # Step 2: Extract visit occurrences
    print("\n🏥 Step 2: Extracting visit occurrences...")
    
    # Group by case to create visits
    visit_groups = claims_df.groupby(['case', 'aio_patient_id']).agg({
        'encounter_start_date': 'first',
        'encounter_end_date': 'first',
        'case_type': 'first'
    }).reset_index()
    
    print(f"   Found {len(visit_groups):,} unique visits")
    
    # Create person_id mapping
    patient_to_person = {patient_id: i+1 for i, patient_id in enumerate(unique_patients)}
    
    visits_data = []
    for i, visit in visit_groups.iterrows():
        visit_record = {
            'visit_occurrence_id': i + 1,
            'person_id': patient_to_person[visit['aio_patient_id']],
            'visit_concept_id': 9202,  # Outpatient visit (default)
            'visit_start_date': pd.to_datetime(visit['encounter_start_date']).date(),
            'visit_end_date': pd.to_datetime(visit['encounter_end_date']).date(),
            'visit_type_concept_id': 32817,  # EHR record
            'visit_source_value': str(visit['case'])
        }
        visits_data.append(visit_record)
    
    print(f"   ✅ Created {len(visits_data):,} visit records")
    
    return persons_data, visits_data

# Execute transformation
if engine and 'claims_df' in locals():
    try:
        persons_data, visits_data = transform_claims_to_omop(claims_df, engine)
        print("\n✅ ETL transformation completed successfully!")
    except Exception as e:
        print(f"\n❌ ETL transformation failed: {str(e)}")
else:
    print("❌ Cannot run ETL - missing database connection or data")

# Data loading with validation
print("📥 DATA LOADING TO OMOP DATABASE")
print("=" * 60)

def load_data_to_omop(persons_data, visits_data, engine):
    """
    Load transformed data to OMOP database with validation.
    
    Learning focus:
    - Transaction management for data integrity
    - Batch loading for performance
    - Error handling and rollback
    - Data validation before commit
    """
    
    from sqlalchemy.orm import sessionmaker
    
    Session = sessionmaker(bind=engine)
    session = Session()
    
    try:
        print("🔄 Starting data loading transaction...")
        
        # Load persons
        print(f"\n👥 Loading {len(persons_data):,} person records...")
        
        for person_data in persons_data:
            person = Person(**person_data)
            session.add(person)
        
        # Flush to get person IDs
        session.flush()
        print("   ✅ Person records staged")
        
        # Load visits
        print(f"\n🏥 Loading {len(visits_data):,} visit records...")
        
        for visit_data in visits_data:
            visit = VisitOccurrence(**visit_data)
            session.add(visit)
        
        session.flush()
        print("   ✅ Visit records staged")
        
        # Validate data before commit
        print("\n🔍 Validating data integrity...")
        
        # Check for orphaned visits
        person_ids = {p['person_id'] for p in persons_data}
        visit_person_ids = {v['person_id'] for v in visits_data}
        orphaned_visits = visit_person_ids - person_ids
        
        if orphaned_visits:
            raise ValueError(f"Found {len(orphaned_visits)} orphaned visits")
        
        print("   ✅ Data integrity validated")
        
        # Commit transaction
        session.commit()
        print("\n✅ Data loading completed successfully!")
        
        return True
        
    except Exception as e:
        session.rollback()
        print(f"\n❌ Data loading failed: {str(e)}")
        print("🔄 Transaction rolled back")
        return False
        
    finally:
        session.close()

# Execute data loading
if engine and 'persons_data' in locals() and 'visits_data' in locals():
    loading_success = load_data_to_omop(persons_data, visits_data, engine)
    
    if loading_success:
        print("🎉 ETL pipeline completed successfully!")
    else:
        print("⚠️ ETL pipeline completed with errors")
else:
    print("❌ Cannot load data - missing prerequisites")

# Comprehensive quality control
print("🔍 COMPREHENSIVE QUALITY CONTROL")
print("=" * 60)

def run_quality_control(engine):
    """
    Run comprehensive quality control checks on OMOP data.
    
    Learning focus:
    - Clinical data validation patterns
    - OMOP-specific quality metrics
    - Interpretation of results in healthcare context
    """
    
    qc_results = {}
    
    with engine.connect() as conn:
        print("📊 Basic Record Counts:")
        
        # Person counts
        person_count = conn.execute(text("SELECT COUNT(*) FROM person")).fetchone()[0]
        qc_results['person_count'] = person_count
        print(f"   👥 Persons: {person_count:,}")
        
        # Visit counts
        visit_count = conn.execute(text("SELECT COUNT(*) FROM visit_occurrence")).fetchone()[0]
        qc_results['visit_count'] = visit_count
        print(f"   🏥 Visits: {visit_count:,}")
        
        # Calculate visits per person
        if person_count > 0:
            visits_per_person = visit_count / person_count
            qc_results['visits_per_person'] = visits_per_person
            print(f"   📈 Visits per person: {visits_per_person:.1f}")
        
        print("\n🔍 Data Quality Checks:")
        
        # Check for orphaned visits
        orphaned_visits = conn.execute(text("""
            SELECT COUNT(*) FROM visit_occurrence v 
            LEFT JOIN person p ON v.person_id = p.person_id 
            WHERE p.person_id IS NULL
        """)).fetchone()[0]
        
        qc_results['orphaned_visits'] = orphaned_visits
        status = "✅" if orphaned_visits == 0 else "❌"
        print(f"   {status} Orphaned visits: {orphaned_visits}")
        
        # Check date consistency
        invalid_dates = conn.execute(text("""
            SELECT COUNT(*) FROM visit_occurrence 
            WHERE visit_start_date > visit_end_date
        """)).fetchone()[0]
        
        qc_results['invalid_dates'] = invalid_dates
        status = "✅" if invalid_dates == 0 else "❌"
        print(f"   {status} Invalid date ranges: {invalid_dates}")
        
        # Check for required fields
        null_person_ids = conn.execute(text("""
            SELECT COUNT(*) FROM person WHERE person_id IS NULL
        """)).fetchone()[0]
        
        qc_results['null_person_ids'] = null_person_ids
        status = "✅" if null_person_ids == 0 else "❌"
        print(f"   {status} Null person IDs: {null_person_ids}")
        
        print("\n📈 Clinical Metrics:")
        
        # Visit duration analysis
        avg_visit_duration = conn.execute(text("""
            SELECT AVG(visit_end_date - visit_start_date) 
            FROM visit_occurrence
        """)).fetchone()[0]
        
        if avg_visit_duration is not None:
            qc_results['avg_visit_duration_days'] = float(avg_visit_duration)
            print(f"   📅 Average visit duration: {avg_visit_duration:.1f} days")
        
        # Date range analysis
        date_range = conn.execute(text("""
            SELECT MIN(visit_start_date), MAX(visit_end_date) 
            FROM visit_occurrence
        """)).fetchone()
        
        if date_range[0] and date_range[1]:
            qc_results['date_range'] = {
                'start': str(date_range[0]),
                'end': str(date_range[1])
            }
            print(f"   📅 Data date range: {date_range[0]} to {date_range[1]}")
    
    return qc_results

# Run quality control
if engine:
    try:
        qc_results = run_quality_control(engine)
        print("\n✅ Quality control completed!")
    except Exception as e:
        print(f"\n❌ Quality control failed: {str(e)}")
        qc_results = {}
else:
    print("❌ Cannot run quality control - no database connection")
    qc_results = {}

# Final project summary
print("🎉 ABU DHABI CLAIMS OMOP MVP - COMPLETION SUMMARY")
print("=" * 70)

# Compile final metrics
final_summary = {
    'project_completion': pd.Timestamp.now().isoformat(),
    'phases_completed': 4,
    'learning_objectives_met': True,
    'technical_deliverables': {
        'omop_database': engine is not None,
        'data_analysis': 'claims_df' in locals(),
        'etl_implementation': 'persons_data' in locals(),
        'quality_control': 'qc_results' in locals()
    },
    'data_metrics': {
        'source_records': len(claims_df) if 'claims_df' in locals() else 0,
        'persons_created': qc_results.get('person_count', 0),
        'visits_created': qc_results.get('visit_count', 0),
        'data_quality_score': 'Excellent' if qc_results.get('orphaned_visits', 1) == 0 else 'Needs improvement'
    }
}

print(f"📅 Project completed: {final_summary['project_completion']}")
print(f"✅ Phases completed: {final_summary['phases_completed']}/4")
print(f"📊 Source records processed: {final_summary['data_metrics']['source_records']:,}")
print(f"👥 OMOP persons created: {final_summary['data_metrics']['persons_created']:,}")
print(f"🏥 OMOP visits created: {final_summary['data_metrics']['visits_created']:,}")
print(f"🎯 Data quality: {final_summary['data_metrics']['data_quality_score']}")

print("\n🚀 NEXT STEPS FOR MAIN PROJECT:")
print("1. Apply learned patterns to FHIR-to-OMOP pipeline")
print("2. Implement vocabulary management system")
print("3. Scale ETL patterns for larger datasets")
print("4. Enhance data quality validation framework")
print("5. Document best practices for team")

print("\n🎓 CONGRATULATIONS!")
print("You have successfully completed the Abu Dhabi Claims OMOP MVP")
print("and gained deep understanding of OMOP CDM implementation!")