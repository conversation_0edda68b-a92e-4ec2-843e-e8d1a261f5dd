# Vocabulary Mappings

This directory contains the vocabulary mappings for transforming Abu Dhabi claims data to OMOP standard concepts.

## Files

- `source_to_concept_map.csv` - Master mapping file for all source codes to OMOP concepts
- `cpt_mappings.csv` - CPT code mappings to OMOP procedure concepts
- `drug_mappings.csv` - Drug code mappings to RxNorm/OMOP drug concepts
- `unmapped_codes.csv` - Codes that could not be mapped to standard concepts
- `mapping_methodology.md` - Documentation of mapping approach and decisions

## Mapping Strategy

### CPT Codes
- Map to OMOP Procedure domain using CPT4 vocabulary
- Target ≥80% mapping success rate
- Document unmapped codes for future investigation

### Drug Codes
- Map to OMOP Drug domain using RxNorm vocabulary
- Handle local drug codes specific to Abu Dhabi healthcare system
- Use concept_id = 0 for unmapped drugs with proper documentation

### Provider and Facility Codes
- Map to appropriate OMOP provider and care_site tables
- Handle local identifier systems

## Quality Criteria

- **Completeness**: Document mapping coverage for each code type
- **Accuracy**: Validate mappings against clinical knowledge
- **Consistency**: Ensure consistent mapping patterns
- **Documentation**: Record rationale for mapping decisions

## External Resources

- [Shafafiya Dictionary](https://www.doh.gov.ae/en/Shafafiya/dictionary)
- [OHDSI Athena](https://athena.ohdsi.org)
- [CMS CPT Code Lookup](https://www.cms.gov/medicare/coding/MedHCPCSGenInfo)

## Next Steps

Mappings are used in the ETL transformation logic to convert source codes to OMOP standard concepts.
